<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HAI Systems — Automatisation & IA pour repreneurs | Diagnostic gratuit</title>
    <meta name="description" content="HAI Systems aide les repreneurs et PME à rendre leurs acquisitions rentables grâce à l'automatisation et l'IA. Diagnostic gratuit 30 min — plan d'action concret.">

    <meta property="og:title" content="HAI Systems — IA & automatisation pour repreneurs">
    <meta property="og:description" content="Rendez vos acquisitions rentables plus vite grâce à l'automatisation et l'IA. Diagnostic gratuit 30 min.">
    <meta property="og:image" content="https://hai-systems.com/preview.jpg">
    <meta property="og:url" content="https://hai-systems.com">
    <meta property="og:type" content="website">

    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="HAI Systems — IA & automatisation pour repreneurs">
    <meta name="twitter:description" content="Diagnostic gratuit 30 min — plan d'action clair et concret.">
    <meta name="twitter:image" content="https://hai-systems.com/preview.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="favicon.ico">

    <!-- Content Security Policy -->
    <meta http-equiv="Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' 'unsafe-inline' 'unsafe-eval'
            https://www.googletagmanager.com
            https://www.google-analytics.com
            https://ssl.google-analytics.com
            https://cdnjs.cloudflare.com;
        style-src 'self' 'unsafe-inline'
            https://fonts.googleapis.com
            https://cdnjs.cloudflare.com;
        font-src 'self'
            https://fonts.gstatic.com
            https://cdnjs.cloudflare.com;
        img-src 'self' data:
            https://www.google-analytics.com
            https://ssl.google-analytics.com;
        connect-src 'self'
            https://www.google-analytics.com
            https://analytics.google.com
            https://region1.google-analytics.com
            https://api.airtable.com;
        frame-src 'none';
        object-src 'none';
        base-uri 'self';
        form-action 'self';
    ">

    <!-- Google Fonts -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet"></noscript>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Font Awesome -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"></noscript>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    
    <style>
        /* Reset et base */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Space Grotesk', sans-serif;
            line-height: 1.6;
            color: var(--text-secondary);
            background: var(--bg-primary);
            overflow-x: hidden;
            transition: background-color var(--transition-theme), color var(--transition-theme);
        }
        
        /* Variables CSS - Système de thèmes Dark/Light */
        :root {
            /* Accents néon rouge (identiques pour les deux thèmes) */
            --neon-red: #ff0040;
            --neon-red-dark: #cc0033;
            --neon-red-light: #ff3366;

            /* Couleurs de statut */
            --success-color: #22c55e;
            --success-bg: rgba(34, 197, 94, 0.1);
            --warning-color: #ffc107;
            --warning-bg: rgba(255, 193, 7, 0.2);

            /* Transitions */
            --transition-fast: 0.2s ease;
            --transition-smooth: 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            --transition-theme: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Thème sombre (par défaut) */
        :root,
        [data-theme="dark"] {
            /* Couleurs principales */
            --bg-primary: #0a0a0a;
            --bg-secondary: #111111;
            --bg-tertiary: #1a1a1a;

            /* Couleurs de texte */
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-muted: #b0bec5;

            /* Effets néon et glow */
            --neon-red-glow: rgba(255, 0, 64, 0.5);

            /* Glassmorphism */
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            --glass-hover-bg: rgba(255, 255, 255, 0.08);

            /* Ombres et effets */
            --shadow-glow: 0 0 20px var(--neon-red-glow);
            --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.5);
            --shadow-hard: 0 8px 40px rgba(0, 0, 0, 0.7);

            /* États interactifs */
            --hover-overlay: rgba(255, 255, 255, 0.05);
            --focus-ring: rgba(255, 0, 64, 0.2);
            --mobile-menu-bg: rgba(10, 10, 10, 0.95);
            --modal-overlay: rgba(0, 0, 0, 0.8);
        }

        /* Thème clair */
        [data-theme="light"] {
            /* Couleurs principales */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;

            /* Couleurs de texte */
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;

            /* Effets néon et glow adaptés pour le mode clair */
            --neon-red-glow: rgba(255, 0, 64, 0.3);

            /* Glassmorphism adapté pour le mode clair */
            --glass-bg: rgba(255, 255, 255, 0.8);
            --glass-border: rgba(0, 0, 0, 0.1);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            --glass-hover-bg: rgba(0, 0, 0, 0.05);

            /* Ombres et effets adaptés */
            --shadow-glow: 0 0 20px var(--neon-red-glow);
            --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.1);
            --shadow-hard: 0 8px 40px rgba(0, 0, 0, 0.15);

            /* États interactifs */
            --hover-overlay: rgba(0, 0, 0, 0.05);
            --focus-ring: rgba(255, 0, 64, 0.2);
            --mobile-menu-bg: rgba(255, 255, 255, 0.95);
            --modal-overlay: rgba(0, 0, 0, 0.5);
        }
        
        /* Typographie */
        .font-mono { font-family: 'JetBrains Mono', monospace; }
        
        .text-hero {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 700;
            line-height: 0.9;
            letter-spacing: -0.02em;
        }
        
        .text-xl {
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            font-weight: 600;
            line-height: 1.2;
        }
        
        .text-lg {
            font-size: clamp(1.125rem, 2.5vw, 1.5rem);
            font-weight: 500;
            line-height: 1.4;
        }
        
        .text-base {
            font-size: 1.125rem;
            line-height: 1.6;
        }
        
        /* Utilitaires */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .section {
            padding: 8rem 0;
        }
        
        .section-sm {
            padding: 4rem 0;
        }
        
        /* Navigation flottante */
        .navbar {
            position: fixed;
            top: 2rem;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10100;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 50px;
            padding: 1.5rem 3rem;
            transition: var(--transition-smooth);
            width: 100%;
            max-width: 1400px;
        }
        
        .navbar:hover {
            background: var(--glass-hover-bg);
            box-shadow: var(--shadow-soft);
        }
        
        .nav-container {
            display: grid;
            grid-template-columns: auto 1fr auto auto auto;
            align-items: center;
            gap: 2rem;
            width: 100%;
            max-width: 1200px;
        }

        .nav-container > * {
            min-width: 0;
        }

        .nav-links {
            display: flex;
            gap: 2.5rem;
            list-style: none;
            justify-content: center;
        }

        .logo {
            font-size: 1.5rem;
            color: var(--text-primary);
            text-decoration: none;
        }

        .logo-hai {
            font-weight: 900;
        }

        .logo-systems {
            font-weight: 400;
        }
        
        .nav-link {
            color: var(--text-muted);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition-fast);
            position: relative;
        }
        
        .nav-link:hover {
            color: var(--neon-red);
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--neon-red);
            transition: var(--transition-smooth);
        }
        
        .nav-link:hover::after {
            width: 100%;
        }

        /* Menu Mobile Hamburger */
        .mobile-menu-toggle {
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: var(--transition-fast);
        }

        .mobile-menu-toggle:hover {
            background: var(--hover-overlay);
        }

        .hamburger-line {
            width: 24px;
            height: 2px;
            background: var(--text-primary);
            margin: 3px 0;
            transition: var(--transition-smooth);
            border-radius: 2px;
        }

        .mobile-menu-toggle.active .hamburger-line:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }

        .mobile-menu-toggle.active .hamburger-line:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu-toggle.active .hamburger-line:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }

        /* BACKUP FILE - TRUNCATED FOR SPACE */
        /* This is a backup of the original index.html file created before refactoring */
        /* The full content is preserved in the original file */
        /* This backup serves as a reference point for validation */
    </style>
</head>
<body>
    <!-- BACKUP FILE CONTENT TRUNCATED -->
    <!-- Full original content preserved for reference -->
    <!-- Use this backup to validate changes and ensure zero regressions -->

    <div style="position: fixed; top: 0; left: 0; right: 0; background: #ff0040; color: white; text-align: center; padding: 1rem; z-index: 99999;">
        🚨 BACKUP FILE - DO NOT USE IN PRODUCTION 🚨
    </div>

    <noscript>
        <div style="background: var(--bg-secondary); color: var(--text-primary); text-align: center; padding: 1rem;">
            <p>This is a backup file. Please use the main index.html file.</p>
        </div>
    </noscript>
</body>
</html>
