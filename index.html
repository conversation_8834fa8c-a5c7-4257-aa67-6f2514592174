<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>HAI Systems — Automatisation & IA pour repreneurs | Diagnostic gratuit</title>
    <meta name="description" content="HAI Systems aide les repreneurs et PME à rendre leurs acquisitions rentables grâce à l'automatisation et l’IA. Diagnostic gratuit 30 min — plan d’action concret.">

    <meta property="og:title" content="HAI Systems — IA & automatisation pour repreneurs">
    <meta property="og:description" content="Rendez vos acquisitions rentables plus vite grâce à l’automatisation et l’IA. Diagnostic gratuit 30 min.">
    <meta property="og:image" content="https://hai-systems.com/preview.jpg">
    <meta property="og:url" content="https://hai-systems.com">
    <meta property="og:type" content="website">

    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="HAI Systems — IA & automatisation pour repreneurs">
    <meta name="twitter:description" content="Diagnostic gratuit 30 min — plan d’action clair et concret.">
    <meta name="twitter:image" content="https://hai-systems.com/preview.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="favicon.ico">

    <!-- Content Security Policy -->
    <meta http-equiv="Content-Security-Policy" content="
        default-src 'self';
        script-src 'self' 'unsafe-inline' 'unsafe-eval'
            https://www.googletagmanager.com
            https://www.google-analytics.com
            https://ssl.google-analytics.com
            https://cdnjs.cloudflare.com;
        style-src 'self' 'unsafe-inline'
            https://fonts.googleapis.com
            https://cdnjs.cloudflare.com;
        font-src 'self'
            https://fonts.gstatic.com
            https://cdnjs.cloudflare.com;
        img-src 'self' data:
            https://www.google-analytics.com
            https://ssl.google-analytics.com;
        connect-src 'self'
            https://www.google-analytics.com
            https://analytics.google.com
            https://region1.google-analytics.com
            https://api.airtable.com;
        frame-src 'none';
        object-src 'none';
        base-uri 'self';
        form-action 'self';
    ">

    <!-- Resource optimization -->
    <link rel="dns-prefetch" href="//www.googletagmanager.com">
    <link rel="dns-prefetch" href="//www.google-analytics.com">
    <link rel="dns-prefetch" href="//api.airtable.com">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet"></noscript>

    <!-- Font Awesome -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"></noscript>
    
    <style>
        /* ========================================
           CSS RESET & BASE STYLES
           ======================================== */

        /* Reset et base */
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Space Grotesk', sans-serif;
            line-height: 1.6;
            color: var(--text-secondary);
            background: var(--bg-primary);
            overflow-x: hidden;
            transition: background-color var(--transition-theme), color var(--transition-theme);
        }

        /* ========================================
           CSS CUSTOM PROPERTIES & THEME SYSTEM
           ======================================== */

        /* Variables CSS - Système de thèmes Dark/Light */
        :root {
            /* Accents néon rouge (identiques pour les deux thèmes) */
            --neon-red: #ff0040;
            --neon-red-dark: #cc0033;
            --neon-red-light: #ff3366;

            /* Couleurs de statut */
            --success-color: #22c55e;
            --success-bg: rgba(34, 197, 94, 0.1);
            --warning-color: #ffc107;
            --warning-bg: rgba(255, 193, 7, 0.2);

            /* Transitions */
            --transition-fast: 0.2s ease;
            --transition-smooth: 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            --transition-theme: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Thème sombre (par défaut) */
        :root,
        [data-theme="dark"] {
            /* Couleurs principales */
            --bg-primary: #0a0a0a;
            --bg-secondary: #111111;
            --bg-tertiary: #1a1a1a;

            /* Couleurs de texte */
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-muted: #b0bec5;

            /* Effets néon et glow */
            --neon-red-glow: rgba(255, 0, 64, 0.5);

            /* Glassmorphism */
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            --glass-hover-bg: rgba(255, 255, 255, 0.08);

            /* Ombres et effets */
            --shadow-glow: 0 0 20px var(--neon-red-glow);
            --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.5);
            --shadow-hard: 0 8px 40px rgba(0, 0, 0, 0.7);

            /* États interactifs */
            --hover-overlay: rgba(255, 255, 255, 0.05);
            --focus-ring: rgba(255, 0, 64, 0.2);
            --mobile-menu-bg: rgba(10, 10, 10, 0.95);
            --modal-overlay: rgba(0, 0, 0, 0.8);
        }

        /* Thème clair */
        [data-theme="light"] {
            /* Couleurs principales */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;

            /* Couleurs de texte */
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-muted: #64748b;

            /* Effets néon et glow adaptés pour le mode clair */
            --neon-red-glow: rgba(255, 0, 64, 0.3);

            /* Glassmorphism adapté pour le mode clair */
            --glass-bg: rgba(255, 255, 255, 0.8);
            --glass-border: rgba(0, 0, 0, 0.1);
            --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            --glass-hover-bg: rgba(0, 0, 0, 0.05);

            /* Ombres et effets adaptés */
            --shadow-glow: 0 0 20px var(--neon-red-glow);
            --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.1);
            --shadow-hard: 0 8px 40px rgba(0, 0, 0, 0.15);

            /* États interactifs */
            --hover-overlay: rgba(0, 0, 0, 0.05);
            --focus-ring: rgba(255, 0, 64, 0.2);
            --mobile-menu-bg: rgba(255, 255, 255, 0.95);
            --modal-overlay: rgba(0, 0, 0, 0.5);
        }

        /* ========================================
           TYPOGRAPHY SYSTEM
           ======================================== */

        /* Typographie */
        .font-mono { font-family: 'JetBrains Mono', monospace; }
        
        .text-hero { font-size: clamp(3rem, 8vw, 6rem); font-weight: 700; line-height: 0.9; letter-spacing: -0.02em; }
        .text-xl { font-size: clamp(1.5rem, 4vw, 2.5rem); font-weight: 600; line-height: 1.2; }
        .text-lg { font-size: clamp(1.125rem, 2.5vw, 1.5rem); font-weight: 500; line-height: 1.4; }
        
        .text-base {
            font-size: 1.125rem;
            line-height: 1.6;
        }
        
        /* Utilitaires */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .section {
            padding: 8rem 0;
        }
        
        .section-sm {
            padding: 4rem 0;
        }

        /* ========================================
           NAVIGATION & HEADER COMPONENTS
           ======================================== */

        /* Navigation flottante */
        .navbar {
            position: fixed;
            top: 2rem;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10100;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 50px;
            padding: 1.5rem 3rem;
            transition: var(--transition-smooth);
            width: 100%;
            max-width: 1400px;
        }
        
        .navbar:hover {
            background: var(--glass-hover-bg);
            box-shadow: var(--shadow-soft);
        }
        
        .nav-container {
            display: grid;
            grid-template-columns: auto 1fr auto auto auto;
            align-items: center;
            gap: 2rem;
            width: 100%;
            max-width: 1200px;
        }

        .nav-container > * {
            min-width: 0;
        }

        .nav-links {
            display: flex;
            gap: 2.5rem;
            list-style: none;
            justify-content: center;
        }

        .logo {
            font-size: 1.5rem;
            color: var(--text-primary);
            text-decoration: none;
        }

        .logo-hai {
            font-weight: 900;
        }

        .logo-systems {
            font-weight: 400;
        }
        
        .nav-link {
            color: var(--text-muted);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition-fast);
            position: relative;
        }
        
        .nav-link:hover {
            color: var(--neon-red);
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--neon-red);
            transition: var(--transition-smooth);
        }
        
        .nav-link:hover::after {
            width: 100%;
        }

        /* Menu Mobile Hamburger */
        .mobile-menu-toggle {
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: var(--transition-fast);
        }

        .mobile-menu-toggle:hover {
            background: var(--hover-overlay);
        }

        .hamburger-line {
            width: 24px;
            height: 2px;
            background: var(--text-primary);
            margin: 3px 0;
            transition: var(--transition-smooth);
            border-radius: 2px;
        }

        .mobile-menu-toggle.active .hamburger-line:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }

        .mobile-menu-toggle.active .hamburger-line:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu-toggle.active .hamburger-line:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }

        /* Menu Mobile Overlay */
        .mobile-menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: var(--mobile-menu-bg);
            backdrop-filter: blur(20px);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .mobile-menu-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .mobile-menu-content {
            display: flex;
            flex-direction: column;
            height: 100%;
            padding: 2rem;
            animation: slideInFromTop 0.3s ease;
        }

        .mobile-menu-overlay.active .mobile-menu-content {
            animation: slideInFromTop 0.3s ease;
        }

        @keyframes slideInFromTop {
            from {
                transform: translateY(-30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .mobile-menu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 3rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--glass-border);
        }

        .mobile-logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .mobile-menu-close {
            background: transparent;
            border: none;
            color: var(--text-primary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: var(--transition-fast);
        }

        .mobile-menu-close:hover {
            background: var(--hover-overlay);
            color: var(--neon-red);
        }

        .mobile-nav {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .mobile-nav-links {
            list-style: none;
            margin: 0;
            padding: 0;
            text-align: center;
        }

        .mobile-nav-links li {
            margin: 1.5rem 0;
        }

        .mobile-nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 1.25rem;
            font-weight: 500;
            transition: var(--transition-fast);
            display: block;
            padding: 1rem;
            border-radius: 12px;
        }

        .mobile-nav-link:hover {
            color: var(--neon-red);
            background: var(--hover-overlay);
            transform: translateX(10px);
        }

        .mobile-cta {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--glass-border);
        }

        /* Indicateurs numériques pour le process */
        .process-step {
            position: relative;
            padding-top: 3rem;
            overflow: visible;
        }

        .step-number {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--text-primary);
            box-shadow: 0 0 20px rgba(255, 0, 64, 0.4);
            border: 2px solid var(--bg-primary);
        }

        .step-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            background: rgba(255, 0, 64, 0.1);
            border-radius: 12px;
            margin: 0 auto 1rem auto;
            color: var(--neon-red);
            font-size: 1.5rem;
        }

        /* FAQ Accordéons */
        .faq-container {
            max-width: 900px;
            margin: 0 auto;
        }

        .faq-item {
            margin-bottom: 1rem;
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            overflow: hidden;
            transition: var(--transition-smooth);
        }

        .faq-item:hover {
            background: var(--glass-hover-bg);
            border-color: rgba(255, 0, 64, 0.3);
        }

        .faq-question {
            width: 100%;
            padding: 1.5rem;
            background: transparent;
            border: none;
            color: var(--text-primary);
            font-size: 1.1rem;
            font-weight: 600;
            text-align: left;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: var(--transition-fast);
        }

        .faq-question:hover {
            color: var(--neon-red);
        }

        .faq-icon {
            font-size: 1rem;
            transition: transform 0.3s ease;
            color: var(--neon-red);
        }

        .faq-item.active .faq-icon {
            transform: rotate(180deg);
        }

        .faq-answer {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
            background: var(--hover-overlay);
        }

        .faq-item.active .faq-answer {
            max-height: 200px;
            padding: 0 1.5rem 1.5rem 1.5rem;
        }

        .faq-answer p {
            color: var(--text-muted);
            line-height: 1.6;
            margin: 0;
        }

        /* Boutons CTA */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: var(--transition-smooth);
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            color: var(--text-primary);
            box-shadow: var(--shadow-glow);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px var(--neon-red-glow);
        }
        
        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: var(--transition-smooth);
        }
        
        .btn-primary:hover::before {
            left: 100%;
        }
        
        .btn-secondary {
            background: var(--glass-bg);
            color: var(--text-primary);
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(10px);
        }
        
        .btn-secondary:hover {
            background: var(--glass-hover-bg);
            border-color: var(--neon-red);
            box-shadow: 0 0 20px rgba(255, 0, 64, 0.3);
        }
        
        .btn-lg {
            padding: 1.5rem 3rem;
            font-size: 1.25rem;
        }
        
        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            z-index: 50;
            background: radial-gradient(ellipse at center, rgba(255, 0, 64, 0.1) 0%, transparent 70%);
            padding-top: 10rem;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 0, 64, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 64, 0.05) 0%, transparent 50%);
            pointer-events: none;
            transform: translateY(var(--parallax-y, 0));
            transition: transform 0.2s linear;
        }
        
        .hero-content {
            position: relative;
            z-index: 20;
            text-align: center;
            max-width: 1000px;
            margin: 0 auto;
            visibility: hidden;
            opacity: 0;
            transition: opacity 220ms ease;
        }
        
        .hero-content.js-visible {
            visibility: visible;
            opacity: 1;
        }
        
        .hero-title {
            margin-bottom: 2rem;
            animation: fadeInUp 1s ease-out;
        }
        
        .hero-subtitle {
            color: var(--text-muted);
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            animation: fadeInUp 1s ease-out 0.2s both;
        }
        
        .hero-cta {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            flex-wrap: wrap;
            animation: fadeInUp 1s ease-out 0.4s both;
        }
        
        .neon-text {
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 0 10px var(--neon-red-glow));
        }
        
        /* Cartes glassmorphism */
        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 3rem;
            transition: var(--transition-smooth);
            position: relative;
            overflow: hidden;
        }
        
        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--neon-red), transparent);
            opacity: 0;
            transition: var(--transition-smooth);
        }
        
        .glass-card.animated:hover {
            transform: translateY(-10px);
            background: var(--glass-hover-bg);
            box-shadow: var(--shadow-hard);
        }
        
        .glass-card:hover::before {
            opacity: 1;
        }
        
        /* Grille de services */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }
        
        .service-icon {
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            font-size: 1.5rem;
            color: var(--text-primary);
            box-shadow: var(--shadow-glow);
        }
        
        /* Témoignages */
        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }
        
        .testimonial {
            position: relative;
        }
        
        .testimonial-quote {
            font-size: 1.25rem;
            font-style: italic;
            margin-bottom: 2rem;
            color: var(--text-secondary);
        }
        
        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .author-avatar {
            width: 3rem;
            height: 3rem;
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .results-badge {
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            color: var(--text-primary);
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            margin-top: 1.5rem;
            display: inline-block;
            box-shadow: var(--shadow-glow);
        }
        
        /* CTA Final */
        .cta-section {
            background: linear-gradient(135deg, rgba(255, 0, 64, 0.1), rgba(255, 0, 64, 0.05));
            border: 1px solid rgba(255, 0, 64, 0.2);
            border-radius: 30px;
            padding: 4rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .cta-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 0, 64, 0.1), transparent);
            animation: rotate 20s linear infinite;
            pointer-events: none;
        }
        
        .cta-content {
            position: relative;
            z-index: 1;
        }
        
        /* Footer avec section À propos intégrée */
        .footer {
            background: var(--bg-secondary);
            border-top: 1px solid var(--glass-border);
            padding: 4rem 0;
        }

        .footer-content {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            align-items: start;
            gap: 3rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-about {
            text-align: center;
        }

        .footer-about h2 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .footer-about h2 .neon-text {
            font-size: 1.5rem;
        }

        .footer-about p {
            color: var(--text-muted);
            line-height: 1.6;
            margin-bottom: 0.75rem;
        }

        .footer-divider {
            width: 1px;
            height: 100%;
            min-height: 200px;
            background: linear-gradient(to bottom, var(--neon-red), transparent);
            opacity: 0.3;
            position: relative;
        }

        .footer-divider::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 3px;
            height: 20px;
            background: var(--neon-red);
            border-radius: 2px;
            box-shadow: 0 0 10px var(--neon-red-glow);
        }

        .footer-info {
            text-align: center;
        }

        .footer-branding {
            margin-bottom: 1.5rem;
        }

        .footer-branding p {
            font-weight: bold;
            color: var(--neon-red);
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .footer-contact {
            margin-bottom: 1.5rem;
        }

        .footer-contact p {
            margin-bottom: 0.5rem;
        }

        .footer-nav {
            margin: 1.5rem 0;
        }

        .footer-nav a {
            color: var(--text-muted);
            text-decoration: none;
            margin: 0 0.5rem;
            transition: var(--transition-fast);
        }

        .footer-nav a:hover {
            color: var(--neon-red);
        }

        .footer-copyright {
            font-size: 0.85rem;
            color: var(--text-muted);
        }

        .footer-copyright a {
            color: var(--text-muted);
            text-decoration: none;
            transition: var(--transition-fast);
        }

        .footer-copyright a:hover {
            color: var(--neon-red);
        }
        
        .footer-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .footer-link {
            color: var(--text-muted);
            text-decoration: none;
            transition: var(--transition-fast);
        }
        
        .footer-link:hover {
            color: var(--neon-red);
        }

        /* Liens de contact (utilisable dans plusieurs sections) */
        .contact-link {
            color: var(--text-muted);
            text-decoration: none;
            transition: var(--transition-fast);
        }

        .contact-link:hover {
            color: var(--neon-red);
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .animate-pulse {
            animation: pulse 2s infinite;
        }
        
        /* Theme Toggle Switch */
        .theme-toggle {
            position: relative;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 26px;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 50px;
            cursor: pointer;
            transition: all var(--transition-theme);
            backdrop-filter: blur(10px);
            padding: 2px;
        }

        .theme-toggle:hover {
            background: var(--glass-hover-bg);
            border-color: rgba(255, 0, 64, 0.3);
            box-shadow: 0 0 15px rgba(255, 0, 64, 0.2);
        }

        .theme-toggle:focus {
            outline: 2px solid var(--neon-red);
            outline-offset: 2px;
            box-shadow: 0 0 0 4px var(--focus-ring);
        }

        .theme-toggle-slider {
            position: absolute;
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            border-radius: 50%;
            transition: all var(--transition-theme);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            left: 2px;
        }

        .theme-toggle-slider::before {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            background: var(--text-primary);
            border-radius: 50%;
            transition: all var(--transition-theme);
        }

        /* Light mode state */
        [data-theme="light"] .theme-toggle-slider {
            transform: translateX(22px);
        }

        [data-theme="light"] .theme-toggle-slider::before {
            background: var(--bg-primary);
        }

        /* Icons inside the toggle */
        .theme-toggle-icon {
            position: absolute;
            font-size: 12px;
            color: var(--text-muted);
            transition: all var(--transition-theme);
            opacity: 0.7;
        }

        .theme-toggle-icon.sun {
            right: 6px;
        }

        .theme-toggle-icon.moon {
            left: 6px;
        }

        [data-theme="light"] .theme-toggle-icon.sun {
            color: var(--neon-red);
            opacity: 1;
        }

        [data-theme="dark"] .theme-toggle-icon.moon {
            color: var(--neon-red);
            opacity: 1;
        }

        /* ========================================
           RESPONSIVE DESIGN - MOBILE (768px and below)
           ======================================== */

        @media (max-width: 768px) {
            /* Theme toggle adjustments */
            .theme-toggle { width: 44px; height: 24px; }
            .theme-toggle-slider { width: 18px; height: 18px; }
            .theme-toggle-slider::before { width: 10px; height: 10px; }
            [data-theme="light"] .theme-toggle-slider { transform: translateX(18px); }
            .theme-toggle-icon { font-size: 10px; }

            /* Container and layout adjustments */
            .container { padding: 0 1rem; }
            .section { padding: 4rem 0; }

            /* Navigation adjustments */
            .navbar { top: 1rem; padding: 0.75rem 1.5rem; min-width: auto; width: calc(100% - 2rem); }
            .nav-container { gap: 1rem; grid-template-columns: auto 1fr auto auto auto; }
            .nav-links { display: none; }
            .mobile-menu-toggle { display: flex; justify-self: end; }
            .navbar .btn { display: none; }
            .theme-toggle { order: -1; }

            /* Layout adjustments */
            .hero { padding-top: 8rem; }
            .hero-cta { flex-direction: column; align-items: center; }
            .services-grid, .testimonials-grid { grid-template-columns: 1fr; }
            .glass-card { padding: 2rem; }
            .cta-section { padding: 2rem; }

            /* Footer responsive */
            .footer-content { grid-template-columns: 1fr; gap: 2rem; text-align: center; }
            .footer-about { text-align: center; order: 1; }
            .footer-about h2 { margin-bottom: 1.5rem; }
            .footer-about p { max-width: none; margin-bottom: 1rem; }
            .footer-divider { display: none; }
            .footer-info { text-align: center; order: 2; }
            .footer-branding { margin-bottom: 2rem; }
            .footer-contact { margin-bottom: 2rem; }
            .footer-nav { margin: 2rem 0; }
            .footer-legal { margin: 1rem 0; font-size: 0.9rem; opacity: 0.8; }
            .footer-copyright { margin-top: 2rem; }
        }

        /* Micro-interactions */
        .interactive-element {
            transition: var(--transition-smooth);
        }

        .interactive-element:hover {
            transform: scale(1.02);
        }

        /* Accessibilité - États de focus */
        *:focus {
            outline: 2px solid var(--neon-red);
            outline-offset: 2px;
        }

        .btn:focus,
        .mobile-nav-link:focus,
        .footer-link:focus,
        .contact-link:focus {
            outline: 2px solid var(--neon-red);
            outline-offset: 2px;
            box-shadow: 0 0 0 4px var(--focus-ring);
        }

        /* Focus plus subtil pour les liens de navigation principale */
        .nav-link:focus {
            outline: none;
            box-shadow: 0 2px 0 var(--neon-red);
            background: var(--focus-ring);
            border-radius: 4px;
        }

        input:focus,
        select:focus,
        textarea:focus {
            outline: none;
            border-color: var(--neon-red);
            box-shadow: 0 0 0 3px var(--focus-ring);
        }

        .faq-question:focus {
            outline: 2px solid var(--neon-red);
            outline-offset: 2px;
            background: var(--hover-overlay);
        }

        .mobile-menu-toggle:focus,
        .mobile-menu-close:focus {
            outline: 2px solid var(--neon-red);
            outline-offset: 2px;
            background: var(--hover-overlay);
        }

        /* Amélioration du contraste pour les éléments interactifs */
        .btn:hover,
        .btn:focus {
            color: var(--text-primary);
        }

        .nav-link:hover,
        .nav-link:focus {
            color: var(--neon-red);
        }

        /* Indicateurs visuels pour les utilisateurs de lecteurs d'écran */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        .sr-only:focus {
            position: absolute;
            width: auto;
            height: auto;
            padding: 0.5rem;
            margin: 0;
            overflow: visible;
            clip: auto;
            white-space: normal;
            background: var(--bg-primary);
            color: var(--text-primary);
            border: 2px solid var(--neon-red);
            border-radius: 4px;
            z-index: 10000;
        }

        /* Amélioration de la lisibilité */
        .text-muted,
        [style*="color:var(--text-muted)"] {
            color: var(--text-muted) !important;
        }

        /* Support pour les préférences de mouvement réduit */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
            }

            .interactive-element:hover {
                transform: none;
            }

            .glass-card:hover {
                transform: none;
            }

            .btn:hover {
                transform: none;
            }

            /* Disable theme toggle animations for reduced motion */
            .theme-toggle,
            .theme-toggle-slider,
            .theme-toggle-icon {
                transition: none !important;
            }

            body {
                transition: none !important;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            :root {
                --glass-bg: rgba(255, 255, 255, 0.9);
                --glass-border: rgba(0, 0, 0, 0.3);
            }

            [data-theme="light"] {
                --glass-bg: rgba(0, 0, 0, 0.9);
                --glass-border: rgba(255, 255, 255, 0.3);
            }

            .theme-toggle {
                border: 2px solid currentColor;
            }
        }

        /* Micro-interactions optimisées pour les appareils tactiles */
        @media (hover: none) and (pointer: coarse) {
            .interactive-element:hover {
                transform: none;
            }

            .glass-card:hover {
                transform: translateY(-5px); /* Effet plus subtil sur tactile */
            }

            .btn:hover {
                transform: none;
            }

            .nav-link:hover::after {
                width: 0; /* Pas d'effet de soulignement sur tactile */
            }
        }

        /* Améliorations des transitions pour une meilleure fluidité */
        .btn,
        .nav-link,
        .mobile-nav-link,
        .glass-card,
        .theme-toggle {
            will-change: transform;
        }

        .btn:not(:hover):not(:focus),
        .glass-card:not(:hover),
        .theme-toggle:not(:hover) {
            will-change: auto;
        }

        /* Optimisation GPU pour les transitions de thème */
        body {
            will-change: background-color, color;
        }

        body:not(.theme-transitioning) {
            will-change: auto;
        }

        .theme-toggle-slider {
            will-change: transform;
        }

        /* Responsive - Grandes tablettes et écrans intermédiaires */
        @media (max-width: 1200px) and (min-width: 1025px) {
            .services-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 1.75rem;
            }

            .testimonials-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.75rem;
            }

            .container {
                max-width: 1000px;
            }
        }

        /* Responsive - Tablettes */
        @media (max-width: 1024px) and (min-width: 769px) {
            .services-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }

            .testimonials-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }

            .container {
                padding: 0 2rem;
            }

            .navbar {
                padding: 1rem 2rem;
                width: calc(100% - 4rem);
            }
        }

        /* Responsive - Petites tablettes */
        @media (max-width: 900px) and (min-width: 769px) {
            .services-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.25rem;
            }

            .testimonials-grid {
                grid-template-columns: 1fr;
                max-width: 600px;
                margin: 0 auto;
            }

            .glass-card {
                padding: 1.5rem;
            }
        }

        /* Ultra-wide screen navbar optimization */
        @media (min-width: 1400px) {
            .nav-container {
                max-width: 1280px;
            }
        }

        /* Responsive navbar adjustments for desktop and large screens */
        @media (max-width: 1199px) and (min-width: 1024px) {
            .navbar {
                padding: 1.25rem 2.5rem;
            }

            .nav-container {
                gap: 3rem;
                max-width: 1000px;
            }
        }

        @media (max-width: 1023px) and (min-width: 900px) {
            .navbar {
                padding: 1rem 2rem;
            }

            .nav-container {
                gap: 2rem;
                max-width: 900px;
            }

            .nav-links {
                gap: 2rem;
            }
        }

        @media (max-width: 899px) and (min-width: 769px) {
            .navbar {
                padding: 1rem 1.5rem;
            }

            .nav-container {
                gap: 1.5rem;
                max-width: 800px;
            }

            .nav-links {
                gap: 1.5rem;
            }

            .navbar .btn {
                padding: 0.875rem 1.5rem;
                font-size: 0.9rem;
            }
        }



        /* ========================================
           RESPONSIVE DESIGN - SMALL MOBILE (480px and below)
           ======================================== */

        @media (max-width: 480px) {
            .text-hero {
                font-size: 2.5rem;
            }

            .btn {
                padding: 0.875rem 1.5rem;
            }

            .btn-lg {
                padding: 1.25rem 2rem;
            }

            /* Responsive improvements for merged section */
            .performance-indicator {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
                margin-top: 0.75rem;
            }

            .results-badge {
                font-size: 0.85rem;
                padding: 0.6rem 1.2rem;
                margin-top: 1rem;
                text-align: center;
                display: block;
                width: fit-content;
                margin-left: auto;
                margin-right: auto;
            }

            .service-icon {
                width: 3rem;
                height: 3rem;
                font-size: 1.2rem;
                margin-bottom: 1.5rem;
            }

            /* Modal adjustments */
            .exemples-modal-overlay { padding: 0.5rem; padding-top: 1rem; }
            .exemples-modal-content { padding: 1.5rem; border-radius: 16px; }
            .exemples-modal-title { font-size: 1.25rem; }
            .exemples-modal-text { font-size: 0.95rem; margin-bottom: 1.5rem; }
            .exemples-modal-btn { min-width: 200px; padding: 0.875rem 1.25rem; font-size: 0.95rem; width: 100%; max-width: 280px; }

            /* Consent banner adjustments */
            .consent-title { font-size: 1rem; }
            .consent-message { font-size: 0.9rem; }
            .consent-btn { padding: 0.875rem 1.25rem; font-size: 0.85rem; }
        }
        
        /* Effets de survol avancés */
        .hover-glow:hover {
            box-shadow: 0 0 30px var(--neon-red-glow);
        }
        
        .hover-lift:hover {
            transform: translateY(-5px);
        }
        
        /* Indicateurs de performance */
        .performance-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--success-bg);
            color: var(--success-color);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.875rem;
            border: 1px solid rgba(34, 197, 94, 0.2);
        }
        
        /* Éléments décoratifs */
        .decoration-dot {
            width: 4px;
            height: 4px;
            background: var(--neon-red);
            border-radius: 50%;
            display: inline-block;
            margin: 0 0.5rem;
            animation: pulse 2s infinite;
        }

        /* Formulaire de diagnostic */
        #diagnostic-form {
            display: flex;
            flex-direction: column;
            gap: 0.875rem;
            max-width: 500px;
            margin: 0 auto;
            margin-bottom: 1.5rem;
        }

        #diagnostic-form input,
        #diagnostic-form select,
        #diagnostic-form textarea {
            padding: 0.75rem 1rem;
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            color: var(--text-primary);
            font-family: 'Space Grotesk', sans-serif;
            font-size: 1rem;
            transition: var(--transition-fast);
            resize: vertical;
            min-height: 48px;
        }

        #diagnostic-form input:focus,
        #diagnostic-form select:focus,
        #diagnostic-form textarea:focus {
            outline: none;
            border-color: var(--neon-red);
            box-shadow: 0 0 0 2px var(--focus-ring);
        }

        /* Correction du dropdown select pour lisibilité */
        #diagnostic-form select {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        #diagnostic-form select option {
            background: var(--bg-secondary);
            color: var(--text-primary);
            padding: 0.5rem;
        }

        #diagnostic-form select option:hover,
        #diagnostic-form select option:focus {
            background: var(--neon-red);
            color: var(--text-primary);
        }

        #diagnostic-form input::placeholder,
        #diagnostic-form textarea::placeholder {
            color: var(--text-muted);
        }

        #diagnostic-form button {
            margin-top: 0.5rem;
            padding: 1rem 2rem;
            align-self: center;
        }

        /* Optimisation formulaire pour desktop */
        @media (min-width: 768px) {
            #diagnostic-form {
                max-width: 500px;
                gap: 1rem;
            }

            #diagnostic-form input,
            #diagnostic-form select,
            #diagnostic-form textarea {
                padding: 0.875rem 1.125rem;
                font-size: 1.05rem;
            }
        }

        @media (min-width: 1024px) {
            #diagnostic-form {
                max-width: 550px;
                gap: 1.125rem;
            }

            #diagnostic-form input,
            #diagnostic-form select,
            #diagnostic-form textarea {
                padding: 1rem 1.25rem;
                font-size: 1.1rem;
            }

            #diagnostic-form button {
                padding: 1.125rem 2rem;
                font-size: 1.1rem;
            }
        }

        @media (min-width: 1200px) {
            #diagnostic-form {
                max-width: 600px;
                gap: 1.25rem;
            }

            #diagnostic-form input,
            #diagnostic-form select,
            #diagnostic-form textarea {
                padding: 1.125rem 1.375rem;
                font-size: 1.125rem;
            }

            #diagnostic-form button {
                padding: 1.25rem 2.25rem;
                font-size: 1.125rem;
            }
        }

        @media (min-width: 1400px) {
            #diagnostic-form {
                max-width: 650px;
                gap: 1.375rem;
            }

            #diagnostic-form input,
            #diagnostic-form select,
            #diagnostic-form textarea {
                padding: 1.25rem 1.5rem;
                font-size: 1.15rem;
            }

            #diagnostic-form button {
                padding: 1.375rem 2.5rem;
                font-size: 1.15rem;
            }
        }

        @media (min-width: 1600px) {
            #diagnostic-form {
                max-width: 700px;
                gap: 1.5rem;
            }

            #diagnostic-form input,
            #diagnostic-form select,
            #diagnostic-form textarea {
                padding: 1.375rem 1.625rem;
                font-size: 1.2rem;
            }

            #diagnostic-form button {
                padding: 1.5rem 2.75rem;
                font-size: 1.2rem;
            }
        }

        /* Timeline du process redesignée pour plus d'intuitivité */
        .process-timeline {
            display: flex;
            flex-direction: column;
            gap: 0;
            margin-top: 3rem;
            position: relative;
            padding: 2rem 0;
        }

        /* Ligne de progression continue */
        .process-timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom,
                var(--neon-red) 0%,
                var(--neon-red) 25%,
                rgba(255, 0, 64, 0.6) 50%,
                rgba(255, 0, 64, 0.3) 75%,
                rgba(255, 0, 64, 0.1) 100%);
            border-radius: 2px;
            transform: translateX(-50%);
            box-shadow: 0 0 10px rgba(255, 0, 64, 0.3);
            z-index: 1;
        }

        .process-step-container {
            display: flex;
            align-items: center;
            gap: 2rem;
            margin-bottom: 3rem;
            position: relative;
        }

        .process-step-container:last-child {
            margin-bottom: 0;
        }

        .process-step {
            position: relative;
            flex: 1;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2.5rem 2rem;
            z-index: 2;
            overflow: visible;
        }

        .process-step .step-number {
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-light));
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.4rem;
            box-shadow: 0 6px 20px rgba(255, 0, 64, 0.4);
            border: 3px solid var(--bg-primary);
            z-index: 3;
            transition: all 0.3s ease;
        }

        .process-step:hover .step-number {
            transform: translateX(-50%) scale(1.1);
            box-shadow: 0 8px 25px rgba(255, 0, 64, 0.6);
        }

        .process-step .step-icon {
            margin: 1.5rem 0 1rem 0;
            font-size: 2.5rem;
            color: var(--neon-red);
            transition: all 0.3s ease;
        }

        .process-step:hover .step-icon {
            transform: scale(1.1);
            filter: drop-shadow(0 0 10px rgba(255, 0, 64, 0.5));
        }

        .process-connector {
            display: none; /* Remplacé par la ligne continue */
        }

        /* Responsive pour la timeline redesignée - Version verticale pour desktop */
        @media (min-width: 768px) {
            .process-timeline {
                flex-direction: column;
                align-items: center;
                gap: 4rem;
                padding: 4rem 0;
                position: relative;
            }

            /* Ligne de progression verticale sur desktop */
            .process-timeline::before {
                left: 50%;
                right: auto;
                top: 0;
                bottom: 0;
                width: 4px;
                height: auto;
                background: linear-gradient(to bottom,
                    var(--neon-red) 0%,
                    var(--neon-red) 25%,
                    rgba(255, 0, 64, 0.6) 50%,
                    rgba(255, 0, 64, 0.3) 75%,
                    rgba(255, 0, 64, 0.1) 100%);
                transform: translateX(-50%);
            }

            .process-step-container {
                width: 100%;
                display: flex;
                justify-content: center;
                position: relative;
                margin-bottom: 0;
            }

            /* Étapes à droite (impaires: 1, 3) */
            .process-step-container:nth-child(odd) {
                justify-content: flex-start;
                padding-left: calc(50% + 3rem);
            }

            /* Étapes à gauche (paires: 2, 4) */
            .process-step-container:nth-child(even) {
                justify-content: flex-end;
                padding-right: calc(50% + 3rem);
                position: relative;
            }

            .process-step {
                position: relative;
                max-width: 400px;
                padding: 2.5rem 2rem;
                overflow: visible;
            }

            .process-step .step-number {
                position: absolute;
                top: 50%;
                z-index: 3;
                width: 50px;
                height: 50px;
                font-size: 1.4rem;
            }

            /* Numéros d'étapes pour les étapes de droite (impaires) - restent inchangés */
            .process-step-container:nth-child(odd) .process-step .step-number {
                left: -25px;
                transform: translateY(-50%);
            }

            /* Numéros d'étapes pour les étapes de gauche (paires) - positionnés symétriquement */
            .process-step-container:nth-child(even) .process-step .step-number {
                right: -25px; /* Position symétrique par rapport aux étapes de droite */
                left: auto;
                transform: translateY(-50%);
            }

            .process-step:hover .step-number {
                transform: translateY(-50%) scale(1.1);
            }

            .process-step .step-icon {
                margin: 1.5rem 0 1rem 0;
                font-size: 2.5rem;
            }

            /* Alignement du texte pour les étapes alternées */
            .process-step-container:nth-child(odd) .process-step {
                text-align: center;
            }

            .process-step-container:nth-child(even) .process-step {
                text-align: center;
            }

            /* Connecteurs visuels entre les numéros et la timeline */
            .process-step .step-number::after {
                content: '';
                position: absolute;
                top: 50%;
                width: 25px;
                height: 2px;
                background: linear-gradient(90deg, var(--neon-red), rgba(255, 0, 64, 0.3));
                transform: translateY(-50%);
                z-index: 2;
            }

            /* Connecteur pour étapes de droite (vers la gauche) */
            .process-step-container:nth-child(odd) .process-step .step-number::after {
                right: 100%;
            }

            /* Connecteur pour étapes de gauche (vers la droite) */
            .process-step-container:nth-child(even) .process-step .step-number::after {
                left: 100%;
                background: linear-gradient(-90deg, var(--neon-red), rgba(255, 0, 64, 0.3));
            }
        }

        @media (max-width: 767px) {
        .process-step {
            position: relative;
            padding-top: 3rem;
            overflow: visible;
        }

            .process-step .step-number {
                width: 45px;
                height: 45px;
                font-size: 1.2rem;
                top: 1px;
            }

            .process-step .step-icon {
                font-size: 2rem;
                margin: 1.2rem 0 1rem 0;
            }

            .process-timeline {
                padding: 1.5rem 0;
            }
        }

        /* Animations de progression pour la timeline */
        @keyframes progressFlow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .process-timeline::before {
            background-size: 200% 200%;
            animation: progressFlow 4s ease-in-out infinite;
        }

        /* Messages d'erreur du formulaire */
        .form-error-message {
            background: rgba(255, 0, 64, 0.1);
            border: 1px solid var(--neon-red);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            color: var(--neon-red);
            font-size: 0.9rem;
            font-weight: 500;
            display: none; /* Hidden by default */
            align-items: center;
            gap: 0.5rem;
            animation: errorSlideIn 0.3s ease;
        }

        /* Show error message when it has content */
        .form-error-message:not(:empty) {
            display: flex;
        }

        .form-error-message::before {
            content: '\f071';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            font-size: 1rem;
        }

        @keyframes errorSlideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Modal de succès */
        .success-modal {
            display: none;
            position: fixed;
            inset: 0;
            background: var(--modal-overlay);
            backdrop-filter: blur(10px);
            z-index: 10000;
            align-items: center;
            justify-content: center;
            animation: modalFadeIn 0.3s ease;
        }

        .success-modal.show {
            display: flex;
        }

        .success-modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 3rem 2rem;
            max-width: 500px;
            width: 90%;
            text-align: center;
            animation: modalSlideIn 0.4s ease;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .success-icon {
            font-size: 4rem;
            color: var(--success-color);
            margin-bottom: 1.5rem;
            animation: successPulse 0.6s ease;
        }

        .success-title {
            color: var(--text-primary);
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .success-message {
            color: var(--text-muted);
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .success-features {
            display: flex;
            justify-content: space-around;
            margin-bottom: 2rem;
            gap: 1rem;
        }

        .success-feature {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        .success-feature i {
            color: var(--neon-red);
            font-size: 1.2rem;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @media (max-width: 768px) {
            .success-features {
                flex-direction: column;
                gap: 1rem;
            }

            .success-feature {
                flex-direction: row;
                justify-content: center;
            }
        }

        /* Études de cas améliorées */
        .case-study-card {
            position: relative;
            overflow: hidden;
        }

        .case-study-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            gap: 1rem;
        }

        .case-study-badge {
            background: var(--warning-bg);
            color: var(--warning-color);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            white-space: nowrap;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .case-study-badge.success {
            background: var(--success-bg);
            color: var(--success-color);
            border-color: rgba(34, 197, 94, 0.3);
        }

        .case-hook {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .case-study-result {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin: 0.75rem auto 0 auto;
            max-width: 90%;
            font-weight: 600;
            color: var(--neon-red);
            position: relative;
            overflow: hidden;
            justify-content: center;
        }

        .case-study-result::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 2s infinite;
        }

        .case-study-result i {
            font-size: 1.2rem;
            color: var(--neon-red);
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* CTA après études de cas */
        .case-study-cta {
            margin-top: 3rem;
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 20px;
            border: 1px solid var(--glass-border);
        }

        .case-study-cta-content h3 {
            margin-bottom: 1rem;
        }

        /* Modal spécifique à la section Exemples représentatifs */
        #exemples {
            position: relative;
        }

        .exemples-modal-overlay {
            position: absolute;
            inset: 0;
            background: transparent;
            backdrop-filter: blur(8px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .exemples-modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .exemples-modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 3rem;
            max-width: 600px;
            width: 100%;
            text-align: center;
            box-shadow: var(--shadow-hard);
            transform: translateY(30px) scale(0.95);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .exemples-modal-overlay.active .exemples-modal-content {
            transform: translateY(0) scale(1);
        }

        .exemples-modal-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--neon-red), transparent);
            opacity: 0.8;
        }

        .exemples-modal-title {
            font-size: clamp(1.5rem, 4vw, 2rem);
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .exemples-modal-text {
            font-size: 1.125rem;
            line-height: 1.6;
            color: var(--text-secondary);
            margin-bottom: 2.5rem;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .exemples-modal-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }

        .exemples-modal-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            padding: 1.25rem 2rem;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: var(--transition-smooth);
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
            min-width: 280px;
            font-family: 'Space Grotesk', sans-serif;
        }

        .exemples-modal-btn-primary {
            background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
            color: var(--text-primary);
            box-shadow: var(--shadow-glow);
        }

        .exemples-modal-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px var(--neon-red-glow);
        }

        .exemples-modal-btn-secondary {
            background: var(--glass-bg);
            color: var(--text-primary);
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(10px);
        }

        .exemples-modal-btn-secondary:hover {
            background: var(--glass-hover-bg);
            border-color: var(--neon-red);
            box-shadow: 0 0 20px rgba(255, 0, 64, 0.3);
        }

        /* Désactiver les interactions avec les cartes quand le modal est actif */
        .exemples-modal-overlay.active ~ .container .case-study-card {
            pointer-events: none;
            opacity: 0.3;
            filter: blur(2px);
            transition: all 0.3s ease;
        }

        /* Assurer que la navigation reste claire et fonctionnelle */
        .exemples-modal-overlay.active ~ .container .case-study-cta {
            filter: blur(2px);
            opacity: 0.3;
            pointer-events: none;
            transition: all 0.3s ease;
        }

        /* S'assurer que seul le contenu de la section est affecté */
        .exemples-modal-overlay.active ~ .container > div:first-child {
            filter: blur(1px);
            opacity: 0.4;
            transition: all 0.3s ease;
        }

        /* Responsive pour le modal */
        @media (max-width: 768px) {
            .exemples-modal-overlay {
                padding: 1rem;
                align-items: flex-start;
                padding-top: 2rem;
            }

            .exemples-modal-content {
                padding: 2rem;
                max-width: 100%;
                margin-top: 1rem;
            }

            .exemples-modal-title {
                font-size: 1.5rem;
                margin-bottom: 1rem;
            }

            .exemples-modal-text {
                font-size: 1rem;
                margin-bottom: 2rem;
                line-height: 1.5;
            }

            .exemples-modal-btn {
                padding: 1rem 1.5rem;
                font-size: 1rem;
                min-width: 250px;
                width: 100%;
                max-width: 300px;
            }
        }



        /* Amélioration pour très petits écrans */
        @media (max-width: 360px) {
            .exemples-modal-content {
                padding: 1rem;
            }

            .exemples-modal-title {
                font-size: 1.1rem;
                line-height: 1.3;
            }

            .exemples-modal-text {
                font-size: 0.9rem;
            }

            .exemples-modal-btn {
                font-size: 0.9rem;
                padding: 0.75rem 1rem;
            }
        }

        @media (max-width: 768px) {
            .case-study-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .case-study-badge {
                align-self: flex-end;
            }
        }

        /* Indicateurs champs obligatoires */
        .field-container {
            position: relative;
        }

        .required-indicator {
            color: var(--neon-red);
            font-weight: 700;
            margin-left: 4px;
            font-size: 1.1rem;
        }

        .field-label {
            display: block;
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .field-error {
            color: var(--neon-red);
            font-size: 0.85rem;
            margin-top: 0.25rem;
            min-height: 1.2rem;
            display: block;
        }

        .field-help {
            color: var(--text-muted);
            font-size: 0.8rem;
            margin-top: 0.25rem;
            font-style: italic;
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

/* --- Styles pour les études de cas --- */
.case-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(320px,1fr)); gap: 1.5rem; margin-top: 1.5rem; }
.case-study-card { position: relative; padding: 1.5rem; cursor: pointer; transition: transform .18s ease, box-shadow .18s ease; border-radius: 16px; text-align: center; }
.case-study-card:hover, .case-study-card:focus { transform: translateY(-6px); box-shadow: 0 12px 30px rgba(0,0,0,0.6); outline: none; }
.case-study-header { display:flex; justify-content:center; align-items:center; gap:1rem; margin-bottom:0.5rem; }
.case-study-badge { font-size:0.8rem; padding:0.35rem 0.6rem; border-radius:999px; background:rgba(255,255,255,0.03); color:var(--text-muted); border:1px solid rgba(255,255,255,0.04); font-weight:600; }
.case-study-badge.success { background: linear-gradient(135deg,var(--neon-red),var(--neon-red-dark)); color:var(--text-primary); box-shadow:var(--shadow-glow); border:none; }
.case-hook { font-weight:600; margin-bottom:0.25rem; }
.case-study-result { display:flex; align-items:center; gap:0.5rem; margin-top:0.75rem; font-weight:600; color:var(--neon-red); }
.case-study-result i { color:var(--neon-red); }
.case-data h4 { margin-top:0.5rem; margin-bottom:0.25rem; color:var(--text-primary); }
.case-data p { color:var(--text-muted); margin-bottom:0.5rem; }

/* CTA block */
.case-study-cta-content { display:inline-block; padding:1.5rem 2rem; border-radius:14px; background:var(--glass-bg); border:1px solid var(--glass-border); }

/* Responsive tweak */
@media (max-width:600px) { .case-grid { grid-template-columns: 1fr; } }

/* HAI Consent Banner Styles - Glassmorphism Integration */
.hai-consent-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10000;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 1.5rem;
    transform: translateY(100%);
    transition: transform var(--transition-smooth);
    font-family: 'Space Grotesk', sans-serif;
}

.hai-consent-banner.show {
    transform: translateY(0);
}

.hai-consent-banner.hide {
    transform: translateY(100%);
}

.consent-container {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.consent-content {
    flex: 1;
    min-width: 0;
}

.consent-title {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.consent-message {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 0;
}

.consent-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
}

.consent-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    border: none;
    font-family: 'Space Grotesk', sans-serif;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.consent-btn:focus {
    outline: 2px solid var(--neon-red);
    outline-offset: 2px;
}

.consent-btn-primary {
    background: linear-gradient(135deg, var(--neon-red), var(--neon-red-dark));
    color: var(--text-primary);
    box-shadow: 0 0 15px rgba(255, 0, 64, 0.3);
}

.consent-btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 0 25px rgba(255, 0, 64, 0.5);
}

.consent-btn-secondary {
    background: var(--glass-bg);
    color: var(--text-secondary);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.consent-btn-secondary:hover {
    background: var(--glass-hover-bg);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.consent-btn-text {
    background: transparent;
    color: var(--text-muted);
    border: none;
    padding: 0.75rem 1rem;
    text-decoration: underline;
    text-underline-offset: 3px;
}

.consent-btn-text:hover {
    color: var(--neon-red);
    text-decoration-color: var(--neon-red);
}

/* Consent Settings Modal */
.consent-settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10001;
    background: var(--modal-overlay);
    backdrop-filter: blur(5px);
    display: none;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.consent-settings-modal.show {
    display: flex;
}

.consent-settings-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--glass-shadow);
}

.consent-settings-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--glass-border);
}

.consent-settings-title {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.consent-close-btn {
    background: transparent;
    border: none;
    color: var(--text-muted);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all var(--transition-fast);
}

.consent-close-btn:hover {
    color: var(--text-primary);
    background: var(--glass-hover-bg);
}

.consent-category {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.consent-category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.consent-category-title {
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

.consent-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 26px;
}

.consent-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.consent-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass-border);
    transition: var(--transition-fast);
    border-radius: 26px;
}

.consent-toggle-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 3px;
    bottom: 3px;
    background: var(--text-muted);
    transition: var(--transition-fast);
    border-radius: 50%;
}

.consent-toggle input:checked + .consent-toggle-slider {
    background: var(--neon-red);
}

.consent-toggle input:checked + .consent-toggle-slider:before {
    transform: translateX(24px);
    background: white;
}

.consent-toggle input:disabled + .consent-toggle-slider {
    opacity: 0.5;
    cursor: not-allowed;
}

.consent-category-description {
    color: var(--text-muted);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.75rem;
}

.consent-cookie-list {
    font-size: 0.8rem;
    color: var(--text-muted);
    opacity: 0.8;
}

.consent-settings-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--glass-border);
}

/* Responsive Design for Consent Banner */
@media (max-width: 768px) {
    .hai-consent-banner {
        padding: 1rem;
    }

    .consent-container {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .consent-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .consent-btn {
        width: 100%;
        text-align: center;
    }

    .consent-settings-modal {
        padding: 1rem;
    }

    .consent-settings-content {
        padding: 1.5rem;
    }

    .consent-settings-actions {
        flex-direction: column;
    }
}


    </style>
</head>
<body>
    <!-- Navigation flottante -->
    <header>
        <nav class="navbar" role="navigation" aria-label="Navigation principale">
            <div class="nav-container">
                <a href="#" class="logo" aria-label="HAI Systems - Retour à l'accueil">
                    <span class="logo-hai">HAI</span> <span class="logo-systems">Systems</span>
                </a>
                <ul class="nav-links" role="menubar">
                    <li role="none"><a href="#accueil" class="nav-link" role="menuitem">Accueil</a></li>
                    <li role="none"><a href="#problemes" class="nav-link" role="menuitem">Problèmes</a></li>
                    <li role="none"><a href="#services" class="nav-link" role="menuitem">Services</a></li>
                    <li role="none"><a href="#process" class="nav-link" role="menuitem">Process</a></li>
                    <li role="none"><a href="#exemples" class="nav-link" role="menuitem">Exemples</a></li>
                    <li role="none"><a href="#pourquoi" class="nav-link" role="menuitem">Pourquoi</a></li>
                </ul>
                <!-- Theme Toggle -->
                <button class="theme-toggle" aria-label="Basculer entre le mode sombre et le mode clair" title="Changer de thème">
                    <span class="theme-toggle-slider" aria-hidden="true"></span>
                    <i class="fas fa-moon theme-toggle-icon moon" aria-hidden="true"></i>
                    <i class="fas fa-sun theme-toggle-icon sun" aria-hidden="true"></i>
                </button>

                <!-- Menu Hamburger (visible uniquement sur mobile) -->
                <button class="mobile-menu-toggle" aria-label="Ouvrir le menu de navigation" aria-expanded="false" aria-controls="mobile-menu">
                    <span class="hamburger-line" aria-hidden="true"></span>
                    <span class="hamburger-line" aria-hidden="true"></span>
                    <span class="hamburger-line" aria-hidden="true"></span>
                </button>
                <a href="#contact" class="btn btn-primary" aria-label="Obtenir un diagnostic gratuit de 30 minutes">
                    <i class="fas fa-rocket" aria-hidden="true"></i>
                    Diagnostic Gratuit
                </a>
            </div>
        </nav>

    <!-- Menu Mobile Overlay -->
    <div class="mobile-menu-overlay" id="mobile-menu">
        <div class="mobile-menu-content">
            <div class="mobile-menu-header">
                <span class="mobile-logo">HAI Systems</span>
                <button class="mobile-menu-close" aria-label="Fermer le menu de navigation">
                    <i class="fas fa-times" aria-hidden="true"></i>
                </button>
            </div>
            <nav class="mobile-nav">
                <ul class="mobile-nav-links">
                    <li><a href="#accueil" class="mobile-nav-link">Accueil</a></li>
                    <li><a href="#problemes" class="mobile-nav-link">Problèmes</a></li>
                    <li><a href="#services" class="mobile-nav-link">Services</a></li>
                    <li><a href="#process" class="mobile-nav-link">Process</a></li>
                    <li><a href="#exemples" class="mobile-nav-link">Exemples</a></li>
                    <li><a href="#pourquoi" class="mobile-nav-link">Pourquoi</a></li>
                </ul>
                <div class="mobile-cta">
                    <a href="#contact" class="btn btn-primary btn-lg" aria-label="Obtenir un diagnostic gratuit (30 min)">
                        <i class="fas fa-rocket" aria-hidden="true"></i>
                        Diagnostic Gratuit
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <main>
        <section class="hero" id="accueil" aria-labelledby="hero-title">
          <div class="container">
        <div class="hero-content">
          <div id="hero-title-container">
            <h1 class="text-hero hero-title" id="hero-title">
              On repère. On automatise. <br><span class="neon-text">On fait gagner de l'argent.</span>
            </h1>
          </div>
          <div id="hero-subtitle-container">
            <p class="text-lg hero-subtitle">
              Solutions (automatisation et IA) clés en main pour repreneurs et PME : diagnostic gratuit de 30 minutes et plan d'action concret.
            </p>
          </div>
          <div class="hero-cta">
            <a id="hero-cta-button" href="#contact" class="btn btn-primary btn-lg hover-glow" aria-label="Obtenir un diagnostic gratuit (30 min)">
              <i class="fas fa-chart-line" aria-hidden="true"></i>
              Obtenir un diagnostic gratuit
            </a>
            <a href="#services" class="btn btn-secondary">
              <i class="fas fa-play" aria-hidden="true"></i>
              Voir nos solutions
            </a>
          </div>
        </div>
      </div>
        </section>
    </main>

    <!-- Problèmes des repreneurs -->
    <section class="section" id="problemes">
      <div class="container">
        <div style="text-align:center; margin-bottom:2rem;">
          <h2 class="text-xl">Les défis des <span class="neon-text">repreneurs</span></h2>
          <p class="text-base" style="color:var(--text-muted); max-width:700px; margin: 0 auto;">
            Reprendre une entreprise, c'est hériter de systèmes obsolètes et de processus inefficaces. Voici les obstacles les plus fréquents.
          </p>
        </div>

        <div class="services-grid">
          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-tasks"></i></div>
            <h3 class="text-lg">Tâches répétitives chronophages</h3>
            <p style="color:var(--text-muted)">Saisie manuelle, reporting Excel, relances clients... Des heures perdues chaque semaine sur des tâches sans valeur ajoutée.</p>
          </div>

          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-unlink"></i></div>
            <h3 class="text-lg">Outils non connectés</h3>
            <p style="color:var(--text-muted)">CRM, comptabilité, stock... Chaque logiciel fonctionne en silo, créant des erreurs et des doublons coûteux.</p>
          </div>

          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-brain"></i></div>
            <h3 class="text-lg">Compétences IA manquantes</h3>
            <p style="color:var(--text-muted)">L'automatisation et l'IA transforment les entreprises, mais les équipes manquent d'expertise pour en tirer parti.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Services (version mise à jour : agents IA mis en avant) -->
    <section class="section" id="services">
      <div class="container">
        <div style="text-align:center; margin-bottom:2rem;">
          <h2 class="text-xl">Solutions <span class="neon-text">Haute Performance</span></h2>
          <p class="text-base" style="color:var(--text-muted); max-width:760px; margin: 0 auto;">
            Pas de techno pour la techno — des systèmes opérationnels qui <strong>réduisent les coûts</strong>, accélèrent la rentabilité après reprise, et remplacent le travail répétitif par des agents IA fiables.
          </p>
        </div>

        <div class="services-grid">
          <!-- Card 1 : Diagnostic & plan d'action -->
          <article class="glass-card interactive-element" aria-labelledby="svc-diagnostic">
            <div class="service-icon" aria-hidden="true"><i class="fas fa-search"></i></div>
            <h3 id="svc-diagnostic" class="text-lg">Diagnostic & plan d'action</h3>
            <p style="color:var(--text-muted)">Audit ciblé sur votre reprise : points de friction, gains rapides, priorisation par ROI.</p>
            <ul style="color:var(--text-muted); margin-top:0.75rem; margin-bottom:1rem; list-style:disc; padding-left:1.25rem;">
              <li>Check-up opérations & outils (3–5 axes prioritaires)</li>
              <li>Feuille de route exécutable — actions classées par valeur</li>
              <li>Livrable prêt à être transformé en devis d'implémentation</li>
              <li>Gratuit, vous décidez vous-mêmes ce que vous en faites</li>
            </ul>
            <div style="margin-top:0.5rem;">
              <a href="#contact" class="btn btn-secondary" aria-label="Réserver diagnostic gratuit (30 min)">Obtenir le diagnostic</a>
            </div>
          </article>

          <!-- Card 2 : Agents IA -->
          <article class="glass-card interactive-element" aria-labelledby="svc-agents">
            <div class="service-icon" aria-hidden="true"><i class="fas fa-robot"></i></div>
            <h3 id="svc-agents" class="text-lg">Agents IA sur-mesure</h3>
            <p style="color:var(--text-muted)">Des agents IA qui deviennent une équipe 24/7 : traitement de tâches répétitives, support client, enrichissement de données, automatisation de workflows.</p>
            <ul style="color:var(--text-muted); margin-top:0.75rem; margin-bottom:1rem; list-style:disc; padding-left:1.25rem;">
              <li>Remplacez le travail manuel par des agents fiables et traçables</li>
              <li>Économies récurrentes + vitesse d'exécution (temps & coûts)</li>
              <li>Sécurité & conformité (accès restreint, logs, audits)</li>
            </ul>
            <div style="margin-top:0.5rem;">
              <a href="#contact" class="btn btn-secondary" aria-label="En savoir plus sur les agents IA">Voir des cas d'usage</a>
            </div>
          </article>

          <!-- Card 3 : Implémentation clé en main -->
          <article class="glass-card interactive-element" aria-labelledby="svc-impl">
            <div class="service-icon" aria-hidden="true"><i class="fas fa-cogs"></i></div>
            <h3 id="svc-impl" class="text-lg">Implémentation clé en main</h3>
            <p style="color:var(--text-muted)">De la maquette au déploiement : intégration API, migration des flux, tests et formation des équipes — livré prêt à l'emploi.</p>
            <ul style="color:var(--text-muted); margin-top:0.75rem; margin-bottom:1rem; list-style:disc; padding-left:1.25rem;">
              <li>Intégration avec vos outils (ERP, CRM, compta, etc.)</li>
              <li>Déploiement minimalement intrusif — pas d'arrêt d'activité</li>
              <li>Documentation et transfert de compétence</li>
            </ul>
            <div style="margin-top:0.5rem;">
              <a href="#contact" class="btn btn-secondary" aria-label="Demander un devis d'implémentation">Demander un devis</a>
            </div>
          </article>

          <!-- Card 4 : Maintenance & optimisation -->
          <article class="glass-card interactive-element" aria-labelledby="svc-maintenance">
            <div class="service-icon" aria-hidden="true"><i class="fas fa-sync-alt"></i></div>
            <h3 id="svc-maintenance" class="text-lg">Maintenance & optimisation</h3>
            <p style="color:var(--text-muted)">Monitoring, KPIs, et itérations régulières pour garder la performance : on corrige, on améliore, on scale.</p>
            <ul style="color:var(--text-muted); margin-top:0.75rem; margin-bottom:1rem; list-style:disc; padding-left:1.25rem;">
              <li>Monitoring proactif et alerting</li>
              <li>Rapports réguliers sur gains & performances</li>
              <li>Améliorations continues priorisées par ROI</li>
            </ul>
            <div style="margin-top:0.5rem;">
              <a href="#contact" class="btn btn-secondary" aria-label="Souscrire maintenance">Voir nos offres de maintenance</a>
            </div>
          </article>
        </div>

        <!-- Ligne de réassurance -->
        <div style="text-align:center; margin-top:2.5rem; color:var(--text-muted);">
          <strong>Cas concret :</strong> réduire le temps de traitement manuel de 30–60% sur des process récurrents grâce à des agents IA. <span class="decoration-dot"></span> <a href="#exemples" class="contact-link">Voir exemples</a>
        </div>
      </div>
    </section>

    <!-- Process -->
    <section class="section" id="process">
      <div class="container">
        <div style="text-align:center; margin-bottom:2rem;">
          <h2 class="text-xl">Comment ça se passe — <span class="neon-text">4 étapes claires</span></h2>
          <p class="text-base" style="color:var(--text-muted); max-width:800px; margin:0 auto;">
            Un parcours simple, orienté ROI : du diagnostic à la mise en production, puis au suivi.
          </p>
        </div>

        <div class="process-timeline">
          <div class="process-step-container">
            <div class="glass-card process-step interactive-element">
              <div class="step-number">1</div>
              <div class="step-icon">
                <i class="fas fa-phone"></i>
              </div>
              <h3 class="text-lg">Appel découverte — 30 min</h3>
              <p style="color:var(--text-muted)">Comprendre votre reprise, vos chiffres clés et vos priorités.</p>
            </div>
            <div class="process-connector">
              <div class="connector-line"></div>
              <div class="connector-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>

          <div class="process-step-container">
            <div class="glass-card process-step interactive-element">
              <div class="step-number">2</div>
              <div class="step-icon">
                <i class="fas fa-search"></i>
              </div>
              <h3 class="text-lg">Diagnostic & plan d'action</h3>
              <p style="color:var(--text-muted)">Livrable priorisé par ROI : feuille de route concrète.</p>
            </div>
            <div class="process-connector">
              <div class="connector-line"></div>
              <div class="connector-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>

          <div class="process-step-container">
            <div class="glass-card process-step interactive-element">
              <div class="step-number">3</div>
              <div class="step-icon">
                <i class="fas fa-cogs"></i>
              </div>
              <h3 class="text-lg">Implémentation clé en main</h3>
              <p style="color:var(--text-muted)">On développe, intègre et livre : vous n'avez rien à gérer techniquement.</p>
            </div>
            <div class="process-connector">
              <div class="connector-line"></div>
              <div class="connector-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>

          <div class="process-step-container">
            <div class="glass-card process-step interactive-element">
              <div class="step-number">4</div>
              <div class="step-icon">
                <i class="fas fa-chart-line"></i>
              </div>
              <h3 class="text-lg">Suivi & optimisation</h3>
              <p style="color:var(--text-muted)">Monitoring, KPIs et itérations pour maximiser le retour.</p>
            </div>
          </div>
        </div>
      </div>
    </section>

<!-- Exemples représentatifs -->
<section class="section" id="exemples">
  <!-- Modal overlay pour la section -->
  <div class="exemples-modal-overlay active" id="exemples-modal" aria-hidden="false" role="dialog" aria-modal="true">
    <div class="exemples-modal-content" role="document">
      <h3 class="exemples-modal-title">Ce portfolio est INUTILE.</h3>
      <div class="exemples-modal-text">
        <p>On a le choix :</p>

        <p>Vous montrer ce qu'on a fait pour d'autres,</p>

        <p style="text-align: center; font-size: 1.1em; margin: 1.5rem 0;"><strong>OU</strong></p>

        <p>Vous montrer ce qu'on va faire <strong>pour VOUS</strong>. 🎯</p>

        <p style="margin-top: 2rem;"><strong>Et ça vous coûtera RIEN.</strong></p>

        <p>Gratuit. Aucun engagement. ✨</p>

        <div style="margin: 2rem 0; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px;">
          <p>→ Vous nous dites <strong>votre plus gros problème actuel</strong>.</p>
          <p>→ On construit une solution ciblée pour l'attaquer si bonne que vous en tomberez <strong>amoureux</strong> <em>(et c'est une <strong>garantie</strong>)</em>. 💝</p>
        </div>

        <p>Si vous n'êtes pas satisfait, vous nous devez rien. 🤝</p>

        <p>Si vous êtes amoureux (comme promis), on pourra enfin parler des prochaines étapes.</p>

        <p style="text-align: center; font-weight: bold; margin-top: 2rem;">Simple. Précis. Zéro perte de temps. ⚡</p>
      </div>
      <div class="exemples-modal-buttons">
        <button class="exemples-modal-btn exemples-modal-btn-primary" id="exemples-modal-contact" aria-label="Obtenir une solution personnalisée">
          <i class="fas fa-rocket"></i>
          Qu'est-ce que vous allez faire pour MOI ?
        </button>
        <button class="exemples-modal-btn exemples-modal-btn-secondary" id="exemples-modal-close" aria-label="Voir les exemples existants">
          <i class="fas fa-eye"></i>
          Qu'est-ce que vous avez fait pour les autres ?
        </button>
      </div>
    </div>
  </div>

  <div class="container">
    <div style="text-align:center; margin-bottom:2rem;">
      <h2 class="text-xl">Exemples représentatifs — <span class="neon-text">résultats concrets</span></h2>
      <p class="text-base" style="color:var(--text-muted); max-width:800px; margin:0 auto;">
        Des transformations mesurables : économies, productivité, trésorerie. Sélection de cas typiques — cliquez pour voir le plan d'action.
      </p>
    </div>

    <div class="testimonials-grid case-grid" role="list">
      <!-- Card 1 -->
      <article class="glass-card case-study-card interactive-element" role="listitem" data-case-id="case-1" tabindex="0" aria-labelledby="case-1-title">
        <div class="case-study-header">
          <h3 id="case-1-title" class="text-lg">PME Distribution — commandes en feu</h3>
        </div>
        <p class="case-hook" style="color:var(--text-muted); margin-bottom:0.75rem;">
          <strong style="font-weight: 700; color: var(--text-primary);">Vous passez vos nuits à corriger des commandes mal saisies.</strong><br><br>On automatise la réception et l'intégration dans votre ERP en reprenant vos règles métier — zéro prise de tête.
        </p>
        <div class="case-study-result">
          <i class="fas fa-chart-line"></i>
          <span><strong>−40% d'erreurs • −25% du temps de traitement.</strong></span>
        </div>
        <div class="case-data" style="display:none">
          <h4>Contexte</h4>
          <p>50% des commandes nécessitaient une correction manuelle, délai moyen 48h.</p>
          <h4>Notre action</h4>
          <p>Prototype agent IA (2 semaines), intégration ERP, tests et déploiement opérationnel.</p>
          <h4>Résultat</h4>
          <p>-40% erreurs, -25% temps de traitement en 8 semaines.</p>
          <p><em>« On a récupéré des marges perdues et des clients satisfaits en moins de deux mois. » — Thomas, CEO</em></p>
        </div>
        <div style="margin-top:1rem; text-align: center;">
          <button class="btn btn-primary" data-case="pme-distribution">Montrer ma solution (pilot)</button>
        </div>
      </article>

      <!-- Card 2 -->
      <article class="glass-card case-study-card interactive-element" role="listitem" data-case-id="case-2" tabindex="0" aria-labelledby="case-2-title">
        <div class="case-study-header">
          <h3 id="case-2-title" class="text-lg">Atelier de production — pannes & trésorerie</h3>
        </div>
        <p class="case-hook" style="color:var(--text-muted); margin-bottom:0.75rem;">
          <strong style="font-weight: 700; color: var(--text-primary);">Les arrêts et le surstock vident la trésorerie à vue d'œil.</strong><br><br>On installe un petit système prédictif + réappro automatique : moins de pannes, moins d'argent immobilisé.
        </p>
        <div class="case-study-result">
          <i class="fas fa-industry"></i>
          <span><strong>−30% coûts d'arrêt • −22% stock moyen.</strong></span>
        </div>
        <div class="case-data" style="display:none">
          <h4>Contexte</h4>
          <p>Stocks surstockés de 18% et arrêts machine fréquents.</p>
          <h4>Notre action</h4>
          <p>Sensorisation minimale, modèle prédictif de maintenance, ajustement planning achats.</p>
          <h4>Résultat</h4>
          <p>-30% coûts d'arrêt, -22% stock moyen.</p>
          <p><em>« Les pannes sont devenues exceptionnelles — trésorerie enfin maîtrisée. » — Laura, Directrice Opérations</em></p>
        </div>
        <div style="margin-top:1rem; text-align: center;">
          <button class="btn btn-primary" data-case="atelier-production">Tester un prototype sur 1 ligne</button>
        </div>
      </article>

      <!-- Card 3 -->
      <article class="glass-card case-study-card interactive-element" role="listitem" data-case-id="case-3" tabindex="0" aria-labelledby="case-3-title">
        <div class="case-study-header">
          <h3 id="case-3-title" class="text-lg">Cabinet / Services — facturation lente</h3>
        </div>
        <p class="case-hook" style="color:var(--text-muted); margin-bottom:0.75rem;">
          <strong style="font-weight: 700; color: var(--text-primary);">On perd du cash parce que la facturation traîne et les relances tombent aux oubliettes.</strong><br><br>On automatise l'extraction, rapproché et relance : facturation plus rapide, trésorerie améliorée.
        </p>
        <div class="case-study-result">
          <i class="fas fa-file-invoice-dollar"></i>
          <span><strong>−70% temps sur tâches répétitives • +15% cash-flow.</strong></span>
        </div>
        <div class="case-data" style="display:none">
          <h4>Contexte</h4>
          <p>3 personnes traitaient 60% des tâches manuelles avec erreurs régulières.</p>
          <h4>Notre action</h4>
          <p>RPA + NLP pour extraction de factures, automatisation rapprochements et workflow d'escalade.</p>
          <h4>Résultat</h4>
          <p>-70% du temps dédié aux tâches répétitives, facturation plus rapide (+15% trésorerie).</p>
          <p><em>« Un gain de temps phénoménal — on facture plus vite et plus proprement. » — Nicolas, Associé</em></p>
        </div>
        <div style="margin-top:1rem; text-align: center;">
          <button class="btn btn-primary" data-case="cabinet-services">Recevoir un mini-prototype factures</button>
        </div>
      </article>

      <!-- Card 4 -->
      <article class="glass-card case-study-card interactive-element" role="listitem" data-case-id="case-4" tabindex="0" aria-labelledby="case-4-title">
        <div class="case-study-header">
          <h3 id="case-4-title" class="text-lg">Logistique / Retail — ruptures & urgences</h3>
        </div>
        <p class="case-hook" style="color:var(--text-muted); margin-bottom:0.75rem;">
          <strong style="font-weight: 700; color: var(--text-primary);">Les ruptures vous coûtent des clients et des marges.</strong><br><br>Forecast simple + commande automatique sur SKUs critiques — on coupe les ruptures sans complexifier vos outils.
        </p>
        <div class="case-study-result">
          <i class="fas fa-shipping-fast"></i>
          <span><strong>−85% ruptures critiques • −18% coûts d'urgence.</strong></span>
        </div>
        <div class="case-data" style="display:none">
          <h4>Contexte</h4>
          <p>Ruptures impactant 10% du CA par mois, commandes express coûteuses.</p>
          <h4>Notre action</h4>
          <p>Forecasting historique + règles de seuil + automatisation commandes fournisseurs.</p>
          <h4>Résultat</h4>
          <p>-85% ruptures critiques, réduction des coûts urgents.</p>
          <p><em>« On a arrêté de courir après les ruptures — trésorerie stabilisée. » — Claire, Responsable Supply</em></p>
        </div>
        <div style="margin-top:1rem; text-align: center;">
          <button class="btn btn-primary" data-case="logistique-retail">Piloter la réappro sur 1 SKU</button>
        </div>
      </article>

      <!-- Card 5 -->
      <article class="glass-card case-study-card interactive-element" role="listitem" data-case-id="case-5" tabindex="0" aria-labelledby="case-5-title">
        <div class="case-study-header">
          <h3 id="case-5-title" class="text-lg">Hôtellerie / Restauration — réservations perdues</h3>
        </div>
        <p class="case-hook" style="color:var(--text-muted); margin-bottom:0.75rem;">
          <strong style="font-weight: 700; color: var(--text-primary);">Des réservations perdent la nuit, les équipes se crèvent sur les pics.</strong><br><br>Agent conversationnel 24/7 relié à votre PMS = réservations captées, staff déchargé.
        </p>
        <div class="case-study-result">
          <i class="fas fa-bed"></i>
          <span><strong>+8% réservations • −30% charge admin.</strong></span>
        </div>
        <div class="case-data" style="display:none">
          <h4>Contexte</h4>
          <p>Réservations perdues la nuit et surcharge aux pics.</p>
          <h4>Notre action</h4>
          <p>Agent IA voice/chat + intégration channel manager et PMS.</p>
          <h4>Résultat</h4>
          <p>+8% réservations traitées 24/7, personnel déchargé.</p>
          <p><em>« Plus zéro demande oubliée la nuit — guests plus satisfaits. » — Marc, Propriétaire</em></p>
        </div>
        <div style="margin-top:1rem; text-align: center;">
          <button class="btn btn-primary" data-case="hotellerie-restauration">Démo live 24/7</button>
        </div>
      </article>

      <!-- Card 6 -->
      <article class="glass-card case-study-card interactive-element" role="listitem" data-case-id="case-6" tabindex="0" aria-labelledby="case-6-title">
        <div class="case-study-header">
          <h3 id="case-6-title" class="text-lg">Agence / SaaS — leads morts</h3>
        </div>
        <p class="case-hook" style="color:var(--text-muted); margin-bottom:0.75rem;">
          <strong style="font-weight: 700; color: var(--text-primary);">Vous générez des leads mais ils tombent dans le vide après la demo.</strong><br>Qualification + nurture automatisés : leads triés, relancés et convertis sans rajouter une armée de commerciaux.
        </p>
        <div class="case-study-result">
          <i class="fas fa-rocket"></i>
          <span><strong>×3 leads qualifiés • conversion doublée (60 j).</strong></span>
        </div>
        <div class="case-data" style="display:none">
          <h4>Contexte</h4>
          <p>Taux lead→RDV 8% avec suivi manuel long.</p>
          <h4>Notre action</h4>
          <p>Qualification automatique, scoring et séquences personnalisées.</p>
          <h4>Résultat</h4>
          <p>3x leads qualifiés, taux de conversion doublé en 60 jours.</p>
          <p><em>« On convertit mieux sans rajouter de commerciaux. » — Julie, Head of Growth</em></p>
        </div>
        <div style="margin-top:1rem; text-align: center;">
          <button class="btn btn-primary" data-case="agence-saas">Tester la qualification sur 100 leads</button>
        </div>
      </article>
    </div>

    <!-- CTA après études de cas -->
    <div class="case-study-cta" style="margin-top:2.5rem; text-align:center;">
      <div class="case-study-cta-content">
        <h3 class="text-lg">Votre entreprise pourrait être la prochaine <span class="neon-text">success story</span></h3>
        <p style="color:var(--text-muted); margin-bottom:1.5rem;">
          Réservez un diagnostic gratuit — 30 minutes. On priorise par ROI, vous décidez ensuite.
        </p>
        <a href="#contact" class="btn btn-primary btn-lg hover-glow" id="case-cta-main">
          <i class="fas fa-rocket"></i>
          Obtenir mon diagnostic gratuit
        </a>
      </div>
    </div>
  </div>
</section>

    <!-- Pourquoi HAI Systems - Merged Section -->
    <section class="section-sm" id="pourquoi">
      <div class="container">
        <div style="text-align:center; margin-bottom:2rem;">
          <h2 class="text-xl">Pourquoi HAI Systems — <span class="neon-text">L'excellence opérationnelle</span></h2>
          <p class="text-base" style="color:var(--text-muted); max-width:700px; margin: 0 auto;">
            Nous transformons vos acquisitions en succès rentables. Notre approche "Hitman" — rapidité, précision, exécution — génère du cash dès les premiers mois.
          </p>
        </div>

        <div class="services-grid" style="margin-top:3rem;">
          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-chart-line"></i></div>
            <h3 class="text-lg">ROI Avant Tout</h3>
            <p style="color:var(--text-muted)">Chaque action évaluée par son impact financier. Priorisation systématique des optimisations à haut retour.</p>
            <div class="performance-indicator" style="margin-top:1rem;">
              <i class="fas fa-bolt"></i>
              ROI moyen : 3-6 mois
            </div>
          </div>

          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-rocket"></i></div>
            <h3 class="text-lg">Exécution Éclair</h3>
            <p style="color:var(--text-muted)">Du diagnostic au déploiement : rapidité et précision. Surnommé "Hitman" pour notre qualité d'exécution.</p>
            <div class="performance-indicator" style="margin-top:1rem;">
              <i class="fas fa-clock"></i>
              Mise en production : 2-4 semaines
            </div>
          </div>

          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-tools"></i></div>
            <h3 class="text-lg">Solutions Clé en Main</h3>
            <p style="color:var(--text-muted)">Design → développement → intégration → maintenance. Vous recevez une solution prête à l'emploi.</p>
            <div class="performance-indicator" style="margin-top:1rem;">
              <i class="fas fa-shield-alt"></i>
              Support technique inclus
            </div>
          </div>

          <div class="glass-card interactive-element">
            <div class="service-icon"><i class="fas fa-trophy"></i></div>
            <h3 class="text-lg">Résultats Garantis</h3>
            <p style="color:var(--text-muted)">Transformations mesurables : réduction des coûts, augmentation de la productivité, optimisation de trésorerie.</p>
            <div class="results-badge" style="margin-top:1rem;">
              <i class="fas fa-chart-bar"></i>
              -25% coûts • +40% productivité
            </div>
          </div>
        </div>

        <div style="margin-top:2rem; text-align:center;">
          <a href="#contact" class="btn btn-primary btn-lg hover-glow" style="padding: 1.5rem 4rem;">
            <i class="fas fa-rocket"></i>
            Démarrer la transformation
          </a>
        </div>
      </div>
    </section>

    <!-- FAQ -->
    <section class="section-sm" id="faq">
      <div class="container">
        <h2 class="text-xl" style="text-align:center; margin-bottom:2rem;">FAQ — <span class="neon-text">questions fréquentes</span></h2>

        <div class="faq-container">
          <div class="faq-item">
            <button class="faq-question" aria-expanded="false">
              Le diagnostic est-il vraiment gratuit ?
              <i class="fas fa-chevron-down faq-icon"></i>
            </button>
            <div class="faq-answer">
              <p>Oui — 30 min d'échange + plan d'action synthétique offert, sans engagement.</p>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question" aria-expanded="false">
              Faut-il des compétences techniques en interne ?
              <i class="fas fa-chevron-down faq-icon"></i>
            </button>
            <div class="faq-answer">
              <p>Non. Nous livrons des solutions prêtes à l'emploi et prenons en charge l'intégration et la maintenance.</p>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question" aria-expanded="false">
              Combien de temps pour voir un ROI ?
              <i class="fas fa-chevron-down faq-icon"></i>
            </button>
            <div class="faq-answer">
              <p>Selon le projet : gains mesurables souvent en 1–3 mois pour optimisations prioritaires.</p>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question" aria-expanded="false">
              Quelle taille d'entreprise ciblez-vous ?
              <i class="fas fa-chevron-down faq-icon"></i>
            </button>
            <div class="faq-answer">
              <p>PME & repreneurs — nous sommes spécialisés en post-reprise.</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Final -->
    <section class="section-sm" id="contact">
      <div class="container">
        <div class="cta-section">
          <div class="cta-content">
            <h2 class="text-xl">Prêt pour la <span class="neon-text">transformation</span> ?</h2>
            <p class="text-base" style="color:var(--text-muted); margin-bottom:1.5rem; max-width:600px; margin-left:auto; margin-right:auto;">
              Diagnostic gratuit de 30 minutes. Plan d'action clair et priorisé par ROI — sans engagement.
            </p>

            <!-- Coordonnées directes -->
            <p style="margin-bottom:1rem;">
              📧 <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a><br>
              📞 <a href="tel:+***********" class="contact-link">+33 7 67 19 99 19</a>
            </p>

            <!-- Formulaire Airtable -->
            <form id="diagnostic-form" autocomplete="on">
              <input type="text" name="name" placeholder="Nom complet" required>
              <input type="text" name="company" placeholder="Entreprise" required>
              <input type="email" name="email" placeholder="Email pro" required>
              <input type="text" name="phone" placeholder="Téléphone (optionnel)">
              <select name="status">
                <option value="reprise">Reprise récente</option>
                <option value="optimisation">Optimisation</option>
                <option value="autre">Autre</option>
              </select>
              <textarea name="context" placeholder="Résumé de votre situation (optionnel)" rows="4"></textarea>

              <!-- Honeypot (invisible to humans) -->
              <input type="text" name="hp" style="display:none" tabindex="-1" autocomplete="off">

              <!-- Timestamp to avoid bots submitting instantly -->
              <input type="hidden" name="ts" id="form-ts">

              <button type="submit" class="btn btn-primary">Réserver mon diagnostic</button>
            </form>

            <div style="margin-top:1.25rem; color:var(--text-muted); font-size:0.95rem;">
              <i class="fas fa-check" style="color:var(--neon-red);"></i> Gratuit & sans engagement
              <span class="decoration-dot"></span>
              <i class="fas fa-clock" style="color:var(--neon-red);"></i> Réponse sous 24h
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- Footer -->
    <footer id="footer" class="section-sm">
      <div class="container">
        <div class="footer">
          <div class="footer-content">
            <!-- Section À propos (gauche) -->
            <div class="footer-about">
              <h2><span class="neon-text">À propos</span></h2>
              <p>HAI Systems conçoit et déploie des systèmes d'automatisation et d'IA pour les repreneurs qui veulent transformer une acquisition en business rentable — vite.</p>
              <p>Approche pragmatique : diagnostic, exécution, optimisation. Surnommé "Hitman" par certains clients pour la qualité d'exécution — discipline, précision, efficacité.</p>
            </div>

            <!-- Diviseur central -->
            <div class="footer-divider"></div>

            <!-- Informations existantes (droite) -->
            <div class="footer-info">
              <div class="footer-branding">
                <h2><span class="neon-text">HAI Systems</span></h2>
              </div>
              <div class="footer-contact">
                <p>
                  📧 <a href="mailto:<EMAIL>" class="footer-link"><EMAIL></a><br>
                  📞 <a href="tel:+***********" class="footer-link">+33 7 67 19 99 19</a>
                </p>
              </div>
              <nav class="footer-nav">
                <a href="#accueil" class="footer-link">Accueil</a> •
                <a href="#services" class="footer-link">Services</a> •
                <a href="#process" class="footer-link">Process</a> •
                <a href="#exemples" class="footer-link">Exemples</a> •
                <a href="#pourquoi" class="footer-link">Pourquoi</a> •
                <a href="#contact" class="footer-link">Contact</a>
              </nav>
              <nav class="footer-legal">
                <a href="/mentions-legales.html" class="footer-link">Mentions légales</a> •
                <a href="/politique-confidentialite.html" class="footer-link">Politique de confidentialité</a> •
                <a href="#" class="footer-link" id="manage-cookies-link">Gérer les cookies</a>
              </nav>
              <div class="footer-copyright">
                <p>© 2025 HAI Systems. Tous droits réservés. | <a href="mentions-legales.html" class="footer-link" target="_blank" rel="noopener noreferrer">Mentions légales</a></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <script>
        /* ========================================
           JAVASCRIPT FUNCTIONALITY
           ========================================

           This script contains all interactive functionality for the HAI Systems landing page:

           1. SCROLL ANIMATIONS - Intersection Observer-based animations for cards and elements
           2. NAVIGATION - Smooth scrolling, mobile menu, theme toggle
           3. INTERACTIVE ELEMENTS - FAQ accordions, case study modals, form handling
           4. GDPR CONSENT MANAGEMENT - Cookie consent system with category-based permissions
           5. ANALYTICS INTEGRATION - Google Analytics with consent-aware loading
           6. A/B TESTING - Hero variant testing with localStorage persistence
           7. FORM HANDLING - Contact form with Airtable integration and environment detection

           All functionality is designed to preserve exact behavior during refactoring.
           ======================================== */

        // Global analytics helper (used throughout the application)
        function safeGtag(eventName, params) {
            try {
                if (window.haiConsent && window.haiConsent.hasConsent('analytics') && typeof gtag === 'function') {
                    gtag('event', eventName, params || {});
                }
            } catch (error) {
                console.warn('Analytics tracking error:', error);
            }
        }

        // Modern Intersection Observer-based scroll animations
        class ScrollAnimationManager {
            constructor() {
                this.animatedElements = new Set();
                this.pendingTimeouts = new Map();
                this.observer = null;
                this.init();
            }

            init() {
                // Create Intersection Observer with optimized settings
                this.observer = new IntersectionObserver(
                    (entries) => this.handleIntersection(entries),
                    {
                        root: null,
                        rootMargin: '0px 0px -15% 0px', // Trigger when 85% visible
                        threshold: [0, 0.1, 0.25, 0.5, 0.75, 1.0] // Multiple thresholds for smooth detection
                    }
                );

                // Observe all animatable elements
                this.observeElements();

                // Debug logging
                console.log('🎬 ScrollAnimationManager initialized with Intersection Observer');
            }

            observeElements() {
                const elements = document.querySelectorAll('.glass-card, .testimonial, .interactive-element');
                let observedCount = 0;
                elements.forEach(element => {
                    // Only observe elements that haven't been animated yet
                    if (!element.classList.contains('animated')) {
                        this.observer.observe(element);
                        observedCount++;
                    }
                });
                console.log(`📊 Observing ${observedCount} elements for scroll animations`);
            }

            handleIntersection(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting && entry.intersectionRatio >= 0.15) {
                        console.log(`✨ Animating element:`, entry.target.className);
                        this.animateElement(entry.target);
                    }
                });
            }

            animateElement(element) {
                // Prevent duplicate animations
                if (this.animatedElements.has(element)) {
                    return;
                }

                // Clear any pending timeout for this element
                if (this.pendingTimeouts.has(element)) {
                    clearTimeout(this.pendingTimeouts.get(element));
                    this.pendingTimeouts.delete(element);
                }

                // Mark as being animated
                this.animatedElements.add(element);

                // Calculate staggered delay based on element position
                const allElements = Array.from(document.querySelectorAll('.glass-card, .testimonial, .interactive-element'));
                const elementIndex = allElements.indexOf(element);
                const delay = Math.min(elementIndex % 6, 5) * 60; // Max 300ms delay, reset every 6 elements

                // Use requestAnimationFrame for smooth timing
                const timeoutId = setTimeout(() => {
                    requestAnimationFrame(() => {
                        // Preserve any existing 3D transforms
                        const currentTransform = element.style.transform;
                        const has3DTransform = currentTransform.includes('perspective') || currentTransform.includes('rotateX') || currentTransform.includes('rotateY');

                        if (has3DTransform) {
                            // For elements with 3D transforms, only update opacity and add animated class
                            element.style.opacity = '1';
                            element.classList.add('animated');
                        } else {
                            // For regular elements, apply full animation
                            element.style.opacity = '1';
                            element.style.transform = 'translateY(0)';
                            element.classList.add('animated');
                        }

                        // Stop observing this element
                        this.observer.unobserve(element);
                        this.pendingTimeouts.delete(element);
                    });
                }, delay);

                this.pendingTimeouts.set(element, timeoutId);
            }

            // Method to clean up timeouts (useful for page unload)
            cleanup() {
                this.pendingTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
                this.pendingTimeouts.clear();
                if (this.observer) {
                    this.observer.disconnect();
                }
            }

            // Method to re-observe new elements (useful for dynamic content)
            refreshObserver() {
                this.observeElements();
            }
        }

        // Global animation manager instance
        let scrollAnimationManager = null;

        // Fallback animation function for browsers without Intersection Observer support
        function fallbackAnimateOnScroll() {
            const elements = document.querySelectorAll('.glass-card, .testimonial, .interactive-element');

            elements.forEach((element, index) => {
                // Skip if already animated
                if (element.classList.contains('animated')) {
                    return;
                }

                const elementTop = element.getBoundingClientRect().top;
                const windowHeight = window.innerHeight;

                // Trigger animation when element is 85% visible
                if (elementTop < windowHeight * 0.85) {
                    const delay = Math.min(index % 6, 5) * 60; // Max 300ms delay, reset every 6 elements

                    setTimeout(() => {
                        requestAnimationFrame(() => {
                            element.style.opacity = '1';
                            element.style.transform = 'translateY(0)';
                            element.classList.add('animated');
                        });
                    }, delay);
                }
            });
        }

        // Throttled scroll handler for fallback
        function createThrottledScrollHandler() {
            let isScrolling = false;
            return function() {
                if (!isScrolling) {
                    requestAnimationFrame(() => { fallbackAnimateOnScroll(); isScrolling = false; });
                    isScrolling = true;
                }
            };
        }

        // Smooth scroll
        function initSmoothScroll() {
            const links = document.querySelectorAll('a[href^="#"]');
            
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const targetId = this.getAttribute('href');
                    const targetSection = document.querySelector(targetId);
                    
                    if (targetSection) {
                        const offsetTop = targetSection.offsetTop - 100;
                        
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }

        // utilitaire pour récupérer UTM si présent
        function getUTMParams() {
            const urlParams = new URLSearchParams(window.location.search);
            return {
                utm_source: urlParams.get('utm_source') || '',
                utm_campaign: urlParams.get('utm_campaign') || ''
            };
        }

        /* ========================================
           FORM HANDLING & AIRTABLE INTEGRATION
           ========================================

           This system handles contact form submissions with:

           ENVIRONMENT DETECTION:
           - Automatic detection of staging vs production
           - Different API endpoints for different environments
           - Fallback mechanism for reliability

           AIRTABLE INTEGRATION:
           - Direct integration with Airtable API for lead storage
           - UTM parameter capture for marketing attribution
           - A/B variant tracking for conversion analysis
           - Consent-aware analytics tracking

           ERROR HANDLING:
           - Primary/fallback endpoint strategy
           - Comprehensive error logging
           - User-friendly error messages
           - Success modal display

           SECURITY:
           - Environment-based endpoint selection
           - CSP-compliant API calls
           - No sensitive data exposure in frontend

           ANALYTICS:
           - Form submission tracking (consent-aware)
           - Variant attribution for A/B testing
           - UTM parameter preservation
           ======================================== */

        // Détection de l'environnement pour choisir le bon endpoint API
        function getApiEndpoint() {
            const hostname = window.location.hostname;
            const protocol = window.location.protocol;
            const isFileProtocol = protocol === 'file:';
            const isLocalhost = hostname.includes('localhost') || hostname.includes('127.0.0.1') || hostname === '';
            const isLocal = isLocalhost && !isFileProtocol;

            // Debug logging
            console.log('🔍 Environment Detection Debug:');
            console.log('  - Hostname:', hostname || '(empty)');
            console.log('  - Protocol:', protocol);
            console.log('  - Is File Protocol:', isFileProtocol);
            console.log('  - Is Localhost:', isLocalhost);
            console.log('  - Is Local Dev:', isLocal);

            // Pour les fichiers locaux (file://), utiliser l'API locale par défaut
            if (isFileProtocol) {
                console.log('  - File protocol detected, using local API endpoint');
                return '/api/lead';
            }

            // Pour localhost en développement, utiliser l'API locale
            if (isLocal) {
                console.log('  - Local development detected, using local API endpoint');
                return '/api/lead';
            }

            // Pour tous les autres cas (staging, production), utiliser Netlify
            console.log('  - Remote environment detected, using Netlify function endpoint');
            return '/.netlify/functions/lead';
        }

        // Fonction de fallback pour essayer les deux endpoints
        async function submitToApi(data) {
            const primaryEndpoint = getApiEndpoint();
            const fallbackEndpoint = primaryEndpoint === '/api/lead' ? '/.netlify/functions/lead' : '/api/lead';

            console.log('🎯 Primary endpoint:', primaryEndpoint);
            console.log('🔄 Fallback endpoint:', fallbackEndpoint);

            try {
                // Essayer l'endpoint principal
                const response = await fetch(primaryEndpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    console.log('✅ Primary endpoint succeeded');
                    return response;
                }

                console.log('⚠️ Primary endpoint failed, trying fallback...');
                throw new Error(`Primary endpoint failed with status ${response.status}`);

            } catch (primaryError) {
                console.log('🔄 Trying fallback endpoint due to:', primaryError.message);

                try {
                    const fallbackResponse = await fetch(fallbackEndpoint, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });

                    if (fallbackResponse.ok) {
                        console.log('✅ Fallback endpoint succeeded');
                        return fallbackResponse;
                    }

                    throw new Error(`Fallback endpoint also failed with status ${fallbackResponse.status}`);

                } catch (fallbackError) {
                    console.error('❌ Both endpoints failed:', {
                        primary: primaryError.message,
                        fallback: fallbackError.message
                    });
                    throw fallbackError;
                }
            }
        }

        const form = document.getElementById('diagnostic-form');
        if (form) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const errorElId = 'form-error-msg';
            let errEl = document.getElementById(errorElId);
            if (!errEl) {
                errEl = document.createElement('div');
                errEl.id = errorElId;
                errEl.className = 'form-error-message';
                form.appendChild(errEl);
            }

            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                // Clear any previous error messages
                errEl.textContent = '';
                errEl.style.display = 'none';
                if (submitBtn) submitBtn.disabled = true;

                // Honeypot anti-spam
                const honeypot = document.getElementById('website');
                if (honeypot && honeypot.value.trim() !== "") {
                  console.warn("Spam détecté — formulaire bloqué.");
                  return;
                }

                const data = Object.fromEntries(new FormData(form).entries());
                const utm = getUTMParams();

                // Combine form data with UTM parameters (timestamp already set on page load)
                const submitData = { ...data, ...utm };

                console.log('📦 Form data:', submitData);
                console.log('🌐 Current hostname:', window.location.hostname);
                console.log('📍 Full URL:', window.location.href);

                try {
                    const res = await submitToApi(submitData);

                    let json = null;
                    try {
                        // Only parse JSON if response has content and is JSON
                        const contentType = res.headers.get('content-type');
                        if (contentType && contentType.includes('application/json')) {
                            const text = await res.text();
                            if (text.trim()) {
                                json = JSON.parse(text);
                            }
                        }
                    } catch (parseError) {
                        console.warn('Failed to parse response as JSON:', parseError);
                        json = { error: 'Invalid response format' };
                    }

                    if (res.ok) {
                        try {
                            // Only track if analytics consent is given
                            if (window.haiConsent && window.haiConsent.hasConsent('analytics') && typeof gtag === 'function') {
                                // Get variant respecting functional cookie consent
                                const variant = (window.haiConsent.hasConsent('functional') ? localStorage.getItem('hero_variant') : null) || 'A';
                                gtag('event', 'form_submitted', {
                                    variant: variant,
                                    utm_source: utm.utm_source || '',
                                    utm_campaign: utm.utm_campaign || ''
                                });
                            }
                        } catch (error) {
                            console.warn('Analytics tracking error:', error);
                        }
                        showSuccessModal();
                        form.reset();
                    } else {
                        console.error('Lead endpoint error', json);
                        errEl.textContent = 'Erreur : impossible d’envoyer votre demande pour le moment. Veuillez réessayer plus tard.';
                        errEl.style.display = 'flex';
                    }
                } catch (err) {
                    console.error('Network or request error:', err);
                    errEl.textContent = "Erreur réseau : vérifiez votre connexion puis réessayez.";
                    errEl.style.display = 'flex';
                } finally {
                    if (submitBtn) submitBtn.disabled = false;
                }
            });
        }

        // Debug function to test environment detection
        function debugEnvironmentDetection() {
            console.log('🔧 === ENVIRONMENT DEBUG INFO ===');
            console.log('Current URL:', window.location.href);
            console.log('Hostname:', window.location.hostname);
            console.log('Protocol:', window.location.protocol);
            console.log('Port:', window.location.port);
            console.log('Detected API endpoint:', getApiEndpoint());
            console.log('User Agent:', navigator.userAgent);
            console.log('=================================');
        }

        // Set timestamp when form is shown/loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Run debug on page load
            debugEnvironmentDetection();
          const tsInput = document.getElementById('form-ts');
          if (tsInput) tsInput.value = Date.now();
        });

        // Effets de parallaxe subtils
        function initParallax() {
          const hero = document.querySelector('.hero');
          if (!hero) return;
          window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset || document.documentElement.scrollTop;
            const y = Math.round(scrolled * 0.03); // ajuster facteur si besoin
            hero.style.setProperty('--parallax-y', `${y}px`);
          });
        }

        // Fonction pour les accordéons FAQ
        function initFAQAccordions() {
            const faqItems = document.querySelectorAll('.faq-item');

            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question');
                const answer = item.querySelector('.faq-answer');

                if (!question || !answer) return;

                question.addEventListener('click', function() {
                    const isActive = item.classList.contains('active');

                    // Fermer tous les autres accordéons
                    faqItems.forEach(otherItem => {
                        if (otherItem !== item) {
                            otherItem.classList.remove('active');
                            const otherQuestion = otherItem.querySelector('.faq-question');
                            if (otherQuestion) {
                                otherQuestion.setAttribute('aria-expanded', 'false');
                            }
                        }
                    });

                    // Toggle l'accordéon actuel
                    if (isActive) {
                        item.classList.remove('active');
                        question.setAttribute('aria-expanded', 'false');
                    } else {
                        item.classList.add('active');
                        question.setAttribute('aria-expanded', 'true');
                    }
                });

                // Support clavier
                question.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        question.click();
                    }
                });
            });
        }

        // Fonction pour le menu mobile
        function initMobileMenu() {
            const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
            const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');
            const mobileMenuClose = document.querySelector('.mobile-menu-close');
            const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

            if (!mobileMenuToggle || !mobileMenuOverlay) return;

            // Fonction pour ouvrir le menu
            function openMobileMenu() {
                mobileMenuOverlay.classList.add('active');
                mobileMenuToggle.classList.add('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'true');
                document.body.style.overflow = 'hidden'; // Empêcher le scroll
            }

            // Fonction pour fermer le menu
            function closeMobileMenu() {
                mobileMenuOverlay.classList.remove('active');
                mobileMenuToggle.classList.remove('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'false');
                document.body.style.overflow = ''; // Restaurer le scroll
            }

            // Event listeners
            mobileMenuToggle.addEventListener('click', function() {
                if (mobileMenuOverlay.classList.contains('active')) {
                    closeMobileMenu();
                } else {
                    openMobileMenu();
                }
            });

            // Fermer avec le bouton X
            if (mobileMenuClose) {
                mobileMenuClose.addEventListener('click', closeMobileMenu);
            }

            // Fermer en cliquant sur un lien de navigation
            mobileNavLinks.forEach(link => {
                link.addEventListener('click', closeMobileMenu);
            });

            // Fermer avec la touche Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && mobileMenuOverlay.classList.contains('active')) {
                    closeMobileMenu();
                }
            });

            // Fermer en cliquant en dehors du menu (sur l'overlay)
            mobileMenuOverlay.addEventListener('click', function(e) {
                if (e.target === mobileMenuOverlay) {
                    closeMobileMenu();
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Style initial pour les animations
            const animatedElements = document.querySelectorAll('.glass-card, .testimonial, .interactive-element');
            animatedElements.forEach(element => {
                // Éviter de dupliquer les styles si l'élément a déjà été traité
                if (!element.classList.contains('animated')) {
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(30px)';
                    element.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                }
            });

            // Initialisation des fonctionnalités
            initFAQAccordions();
            initMobileMenu();
            initSmoothScroll();
            initParallax();
            initCardInteractions();

            // Initialize scroll animation system with fallback
            if ('IntersectionObserver' in window) {
                // Use modern Intersection Observer
                scrollAnimationManager = new ScrollAnimationManager();

                // Cleanup on page unload
                window.addEventListener('beforeunload', () => {
                    if (scrollAnimationManager) {
                        scrollAnimationManager.cleanup();
                    }
                });
            } else {
                // Fallback for older browsers
                console.log('Using fallback scroll animation system');
                const throttledScrollHandler = createThrottledScrollHandler();

                // Initial animation check
                setTimeout(fallbackAnimateOnScroll, 500);

                // Scroll event listener
                window.addEventListener('scroll', throttledScrollHandler);

                // Cleanup on page unload
                window.addEventListener('beforeunload', () => {
                    window.removeEventListener('scroll', throttledScrollHandler);
                });
            }
        });

        // Micro-interactions optimisées avec effet de rotation amélioré
        function initCardInteractions() {
            // Vérifier si l'utilisateur préfère un mouvement réduit
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

            // Détecter les vrais appareils mobiles (pas les ordinateurs avec écran tactile)
            const isMobileDevice = window.matchMedia('(hover: none) and (pointer: coarse)').matches;

            // Ne pas appliquer les effets de rotation sur les vrais appareils mobiles ou si mouvement réduit préféré
            if (isMobileDevice || prefersReducedMotion) {
                return;
            }

            let isThrottled = false;
            let animationId = null;

            document.addEventListener('mousemove', function(e) {
                // Throttling optimisé pour améliorer les performances
                if (isThrottled) return;
                isThrottled = true;

                // Annuler l'animation précédente si elle existe
                if (animationId) {
                    cancelAnimationFrame(animationId);
                }

                animationId = requestAnimationFrame(() => {
                    const cards = document.querySelectorAll('.glass-card:not(.no-tilt)');

                    cards.forEach(card => {
                        const rect = card.getBoundingClientRect();
                        const x = e.clientX - rect.left;
                        const y = e.clientY - rect.top;

                        if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
                            const centerX = rect.width / 2;
                            const centerY = rect.height / 2;

                            // Effet plus visible et fluide (divisé par 20 pour moins de sensibilité)
                            const rotateX = (y - centerY) / 20;
                            const rotateY = (centerX - x) / 20;

                            // Effet de profondeur plus prononcé
                            const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));
                            const maxDistance = Math.sqrt(Math.pow(centerX, 2) + Math.pow(centerY, 2));
                            const translateZ = (1 - distance / maxDistance) * 11 + 5; // Entre 5px et 16px (~30% moins)

                            // Effet d'ombre dynamique
                            const shadowIntensity = (1 - distance / maxDistance) * 0.3 + 0.1;
                            const shadowX = (x - centerX) / 20;
                            const shadowY = (y - centerY) / 20;

                            // Improved transform state management
                            const isAnimated = card.classList.contains('animated');
                            const baseTransform = isAnimated ? 'translateY(0) ' : 'translateY(30px) ';

                            // Apply 3D transform while preserving scroll animation state
                            card.style.transform = `${baseTransform}perspective(1200px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(${translateZ}px)`;
                            card.style.boxShadow = `${shadowX}px ${shadowY}px ${20 + translateZ}px rgba(0, 0, 0, ${shadowIntensity}), 0 0 ${translateZ}px rgba(255, 0, 64, 0.1)`;
                            card.style.transition = 'box-shadow 0.1s ease-out';

                            // Ensure opacity is maintained for animated elements
                            if (isAnimated && card.style.opacity !== '1') {
                                card.style.opacity = '1';
                            }
                        } else {
                            // Smooth transition to rest state while preserving scroll animation
                            const isAnimated = card.classList.contains('animated');
                            const baseTransform = isAnimated ? 'translateY(0) ' : 'translateY(30px) ';

                            card.style.transform = `${baseTransform}perspective(1200px) rotateX(0deg) rotateY(0deg) translateZ(0px)`;
                            card.style.boxShadow = '';
                            card.style.transition = 'transform 0.3s ease-out, box-shadow 0.3s ease-out';

                            // Maintain proper opacity based on animation state
                            if (isAnimated && card.style.opacity !== '1') {
                                card.style.opacity = '1';
                            }
                        }
                    });

                    isThrottled = false;
                    animationId = null;
                });
            });

            // Réinitialiser les transformations quand la souris quitte la fenêtre
            document.addEventListener('mouseleave', function() {
                const cards = document.querySelectorAll('.glass-card');
                cards.forEach(card => {
                    const isAnimated = card.classList.contains('animated');
                    const baseTransform = isAnimated ? 'translateY(0) ' : 'translateY(30px) ';

                    card.style.transform = `${baseTransform}perspective(1200px) rotateX(0deg) rotateY(0deg) translateZ(0px)`;
                    card.style.boxShadow = '';
                    card.style.transition = 'transform 0.3s ease-out, box-shadow 0.3s ease-out';

                    // Maintain proper opacity based on animation state
                    if (isAnimated && card.style.opacity !== '1') {
                        card.style.opacity = '1';
                    }
                });
            });
        }
    </script>

    <!-- Theme Toggle Functionality -->
    <script>
        class ThemeManager {
            constructor() {
                this.currentTheme = 'dark'; // Default theme
                this.toggleButton = null;
                this.init();
            }

            init() {
                // Get saved theme from localStorage or detect system preference
                this.currentTheme = this.getInitialTheme();

                // Apply the theme immediately to prevent flash
                this.applyTheme(this.currentTheme, false);

                // Wait for DOM to be ready
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', () => this.setupToggle());
                } else {
                    this.setupToggle();
                }
            }

            getInitialTheme() {
                return localStorage.getItem('theme') ||
                       (window.matchMedia && window.matchMedia('(prefers-color-scheme: light)').matches ? 'light' : 'dark');
            }

            setupToggle() {
                this.toggleButton = document.querySelector('.theme-toggle');
                if (this.toggleButton) {
                    this.toggleButton.addEventListener('click', () => this.toggleTheme());

                    // Add keyboard support
                    this.toggleButton.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            this.toggleTheme();
                        }
                    });
                }
            }

            toggleTheme() {
                const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
                this.applyTheme(newTheme, true);
                this.currentTheme = newTheme;

                // Save to localStorage
                localStorage.setItem('theme', newTheme);

                // Update button aria-label for accessibility
                if (this.toggleButton) {
                    const label = newTheme === 'dark'
                        ? 'Basculer vers le mode clair'
                        : 'Basculer vers le mode sombre';
                    this.toggleButton.setAttribute('aria-label', label);
                    this.toggleButton.setAttribute('title', label);
                }

                // Announce theme change to screen readers
                this.announceThemeChange(newTheme);

                // Trigger custom event for other components that might need to react
                window.dispatchEvent(new CustomEvent('themeChanged', {
                    detail: { theme: newTheme }
                }));
            }

            announceThemeChange(theme) {
                // Create a live region announcement for screen readers
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'polite');
                announcement.setAttribute('aria-atomic', 'true');
                announcement.className = 'sr-only';
                announcement.textContent = `Thème changé vers ${theme === 'dark' ? 'sombre' : 'clair'}`;

                document.body.appendChild(announcement);

                // Remove the announcement after it's been read
                setTimeout(() => {
                    document.body.removeChild(announcement);
                }, 1000);
            }

            applyTheme(theme, animate = true) {
                const root = document.documentElement;
                const body = document.body;

                // Add transition class for smooth animation and performance optimization
                if (animate) {
                    body.classList.add('theme-transitioning');
                    root.style.transition = 'background-color 0.3s ease, color 0.3s ease';

                    // Remove transition and performance class after animation completes
                    setTimeout(() => {
                        root.style.transition = '';
                        body.classList.remove('theme-transitioning');
                    }, 300);
                }

                // Apply theme data attribute
                root.setAttribute('data-theme', theme);

                // Update meta theme-color for mobile browsers
                this.updateMetaThemeColor(theme);
            }

            updateMetaThemeColor(theme) {
                let metaThemeColor = document.querySelector('meta[name="theme-color"]');
                if (!metaThemeColor) {
                    metaThemeColor = document.createElement('meta');
                    metaThemeColor.name = 'theme-color';
                    document.head.appendChild(metaThemeColor);
                }

                const color = theme === 'dark' ? '#0a0a0a' : '#ffffff';
                metaThemeColor.content = color;
            }

            getCurrentTheme() {
                return this.currentTheme;
            }
        }

        // Initialize theme manager
        window.themeManager = new ThemeManager();
    </script>


    <!-- HAI Consent Management System -->
    <script>
        /* ========================================
           GDPR CONSENT MANAGEMENT SYSTEM
           ========================================

           This is a comprehensive GDPR-compliant cookie consent system designed specifically
           for HAI Systems with the following features:

           ARCHITECTURE:
           - Category-based consent (essential, analytics, functional)
           - localStorage persistence with expiration (12 months)
           - Consent-aware loading of third-party scripts (Google Analytics)
           - Public API for other scripts to check consent status

           USER EXPERIENCE:
           - Non-overwhelming banner with casual/approachable tone
           - Acknowledges legal obligation regretfully (per user preference)
           - Granular control with "Personnaliser" option
           - Glassmorphism design integration

           TECHNICAL FEATURES:
           - Automatic consent expiration and renewal
           - Environment-aware debugging (staging vs production)
           - Event callbacks for consent changes
           - Safe analytics tracking with consent checks
           - Accessibility compliant (ARIA labels, keyboard navigation)

           INTEGRATION:
           - Google Analytics conditional loading
           - A/B testing localStorage respect
           - Form tracking with consent awareness
           - Cross-script consent checking via window.HAICookies API
           ======================================== */

        // HAI Consent Management System - Core Architecture
        class HAIConsentManager {
            constructor() {
                this.version = '1.0';
                this.consentData = this.loadConsent();
                this.cookieCategories = this.initializeCookieCategories();
                this.callbacks = {
                    onConsentChange: [],
                    onBannerShow: [],
                    onBannerHide: []
                };
                this.bannerElement = null;
                this.isInitialized = false;

                // Initialize the system
                this.init();
            }

            // Initialize cookie categories according to GDPR requirements
            initializeCookieCategories() {
                return {
                    essential: {
                        name: 'Cookies essentiels',
                        description: 'Nécessaires au fonctionnement du site (thème, préférences)',
                        required: true,
                        cookies: [
                            {
                                name: 'theme',
                                purpose: 'Sauvegarde du thème sombre/clair choisi',
                                duration: 'Permanent',
                                domain: window.location.hostname
                            },
                            {
                                name: 'hai_consent',
                                purpose: 'Sauvegarde de vos préférences de cookies',
                                duration: '12 mois',
                                domain: window.location.hostname
                            }
                        ]
                    },
                    analytics: {
                        name: 'Cookies analytiques',
                        description: 'Nous aident à comprendre comment vous utilisez le site',
                        required: false,
                        cookies: [
                            {
                                name: '_ga',
                                purpose: 'Google Analytics - Identifiant unique des visiteurs',
                                duration: '2 ans',
                                domain: '.hai-systems.com',
                                provider: 'Google Analytics'
                            },
                            {
                                name: '_ga_*',
                                purpose: 'Google Analytics - Données de session',
                                duration: '2 ans',
                                domain: '.hai-systems.com',
                                provider: 'Google Analytics'
                            },
                            {
                                name: '_gid',
                                purpose: 'Google Analytics - Identifiant de session',
                                duration: '24 heures',
                                domain: '.hai-systems.com',
                                provider: 'Google Analytics'
                            }
                        ]
                    },
                    marketing: {
                        name: 'Cookies marketing',
                        description: 'Utilisés pour la publicité personnalisée (aucun actuellement)',
                        required: false,
                        cookies: []
                    },
                    functional: {
                        name: 'Cookies fonctionnels',
                        description: 'Améliorent votre expérience (variantes A/B)',
                        required: false,
                        cookies: [
                            {
                                name: 'hero_variant',
                                purpose: 'Sauvegarde de la variante A/B affichée',
                                duration: 'Session',
                                domain: window.location.hostname
                            }
                        ]
                    }
                };
            }

            // Initialize the consent management system
            init() {
                if (this.isInitialized) return;

                console.log('🍪 HAI Consent Manager initializing...');

                // Check if consent is needed
                if (this.needsConsent()) {
                    this.showConsentBanner();
                } else {
                    // Load analytics if consent was previously given
                    if (this.hasConsent('analytics')) {
                        this.loadGoogleAnalytics();
                    }
                }

                this.isInitialized = true;
                console.log('✅ HAI Consent Manager initialized');
            }

            // Load existing consent from localStorage
            loadConsent() {
                try {
                    const stored = localStorage.getItem('hai_consent');
                    if (stored) {
                        const consent = JSON.parse(stored);

                        // Check if consent is still valid (not expired)
                        if (consent.expires && new Date(consent.expires) > new Date()) {
                            return consent;
                        }
                    }
                } catch (error) {
                    console.warn('Error loading consent data:', error);
                }

                // Return default consent state (only essential cookies)
                return {
                    version: this.version,
                    timestamp: new Date().toISOString(),
                    essential: true,
                    analytics: false,
                    marketing: false,
                    functional: false,
                    expires: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString() // 1 year
                };
            }

            // Save consent to localStorage
            saveConsent() {
                try {
                    localStorage.setItem('hai_consent', JSON.stringify(this.consentData));
                    console.log('💾 Consent preferences saved');
                } catch (error) {
                    console.error('Error saving consent:', error);
                }
            }

            // Check if user needs to see consent banner
            needsConsent() {
                // Always need consent if no previous consent exists
                if (!localStorage.getItem('hai_consent')) {
                    return true;
                }

                // Check if consent has expired
                if (this.consentData.expires && new Date(this.consentData.expires) <= new Date()) {
                    return true;
                }

                // Check if consent version has changed
                if (this.consentData.version !== this.version) {
                    return true;
                }

                return false;
            }

            // Check if user has given consent for a specific category
            hasConsent(category) {
                return this.consentData[category] === true;
            }

            // Set consent for a specific category
            setConsent(category, granted) {
                this.consentData[category] = granted;
                this.consentData.timestamp = new Date().toISOString();
                this.saveConsent();

                // Trigger callbacks
                this.triggerCallbacks('onConsentChange', { category, granted });

                // Handle analytics loading/unloading
                if (category === 'analytics') {
                    if (granted) {
                        this.loadGoogleAnalytics();
                    } else {
                        this.unloadGoogleAnalytics();
                    }
                }
            }

            // Accept all non-essential cookies
            acceptAll() {
                this.setConsent('analytics', true);
                this.setConsent('marketing', true);
                this.setConsent('functional', true);
                this.hideConsentBanner();
            }

            // Accept only essential cookies
            acceptEssentialOnly() {
                this.setConsent('analytics', false);
                this.setConsent('marketing', false);
                this.setConsent('functional', false);
                this.hideConsentBanner();
            }

            // Load Google Analytics conditionally
            loadGoogleAnalytics() {
                if (window.gtag) {
                    console.log('📊 Google Analytics already loaded');
                    return;
                }

                console.log('📊 Loading Google Analytics with consent...');

                // Create and load gtag script
                const script = document.createElement('script');
                script.async = true;
                script.src = 'https://www.googletagmanager.com/gtag/js?id=G-X6B0VTFPDD';
                document.head.appendChild(script);

                // Initialize gtag
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                window.gtag = gtag;
                gtag('js', new Date());
                gtag('config', 'G-X6B0VTFPDD');

                console.log('✅ Google Analytics loaded');
            }

            // Unload Google Analytics (for consent withdrawal)
            unloadGoogleAnalytics() {
                console.log('🚫 Unloading Google Analytics...');

                // Clear Google Analytics cookies
                this.clearGoogleAnalyticsCookies();

                // Note: We can't fully unload gtag once loaded, but we stop tracking
                if (window.gtag) {
                    window.gtag('config', 'G-X6B0VTFPDD', {
                        'send_page_view': false
                    });
                }
            }

            // Clear Google Analytics cookies
            clearGoogleAnalyticsCookies() {
                const gaCookies = ['_ga', '_gid', '_gat', '_gat_gtag_UA_', '_gat_gtag_G_'];
                gaCookies.forEach(cookieName => {
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname}`;
                    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname}`;
                });
            }

            // Register callback for consent changes
            onConsentChange(callback) {
                this.callbacks.onConsentChange.push(callback);
            }

            // Trigger callbacks
            triggerCallbacks(event, data) {
                this.callbacks[event].forEach(callback => {
                    try {
                        callback(data);
                    } catch (error) {
                        console.error('Callback error:', error);
                    }
                });
            }

            // Show consent banner
            showConsentBanner() {
                console.log('🍪 Showing consent banner...');

                if (this.bannerElement) {
                    this.bannerElement.classList.add('show');
                    return;
                }

                this.createConsentBanner();
                this.triggerCallbacks('onBannerShow', {});
            }

            // Hide consent banner
            hideConsentBanner() {
                console.log('✅ Hiding consent banner...');

                if (this.bannerElement) {
                    this.bannerElement.classList.remove('show');
                    this.bannerElement.classList.add('hide');

                    // Remove from DOM after animation
                    setTimeout(() => {
                        if (this.bannerElement && this.bannerElement.parentNode) {
                            this.bannerElement.parentNode.removeChild(this.bannerElement);
                            this.bannerElement = null;
                        }
                    }, 400);
                }

                this.triggerCallbacks('onBannerHide', {});
            }

            // Create consent banner HTML
            createConsentBanner() {
                const banner = document.createElement('div');
                banner.className = 'hai-consent-banner';
                banner.setAttribute('role', 'dialog');
                banner.setAttribute('aria-labelledby', 'consent-title');
                banner.setAttribute('aria-describedby', 'consent-message');

                banner.innerHTML = `
                    <div class="consent-container">
                        <div class="consent-content">
                            <h3 id="consent-title" class="consent-title">
                                🍪 Comme tout le monde, on n'aime pas les cookies...
                            </h3>
                            <p id="consent-message" class="consent-message">
                                Mais comme tout le monde, on utilise des cookies pour améliorer votre expérience. On est légalement obligés de vous le dire, mais <strong>on vous laisse TOUJOURS le dernier mot</strong> (<strong><em>comme après TOUS les Diagnostics Gratuits qu'on vous offre... 👀</em></strong>).
                            </p>
                        </div>
                        <div class="consent-actions">
                            <button class="consent-btn consent-btn-primary" id="consent-accept-all" aria-label="Accepter tous les cookies">
                                Tout accepter
                            </button>
                            <button class="consent-btn consent-btn-secondary" id="consent-essential-only" aria-label="Accepter uniquement les cookies essentiels">
                                Essentiel seulement
                            </button>
                            <button class="consent-btn consent-btn-text" id="consent-customize" aria-label="Personnaliser les préférences de cookies">
                                Personnaliser
                            </button>
                        </div>
                    </div>
                `;

                // Add event listeners
                this.setupBannerEventListeners(banner);

                // Add to DOM
                document.body.appendChild(banner);
                this.bannerElement = banner;

                // Show with animation
                setTimeout(() => {
                    banner.classList.add('show');
                }, 100);

                // Focus management for accessibility
                const acceptButton = banner.querySelector('#consent-accept-all');
                if (acceptButton) {
                    acceptButton.focus();
                }
            }

            // Setup event listeners for banner buttons
            setupBannerEventListeners(banner) {
                const acceptAllBtn = banner.querySelector('#consent-accept-all');
                const essentialOnlyBtn = banner.querySelector('#consent-essential-only');
                const customizeBtn = banner.querySelector('#consent-customize');

                acceptAllBtn?.addEventListener('click', () => {
                    this.acceptAll();
                    this.trackConsentEvent('accept_all');
                });

                essentialOnlyBtn?.addEventListener('click', () => {
                    this.acceptEssentialOnly();
                    this.trackConsentEvent('essential_only');
                });

                customizeBtn?.addEventListener('click', () => {
                    this.showConsentSettings();
                    this.trackConsentEvent('customize_clicked');
                });

                // Keyboard navigation
                banner.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        this.acceptEssentialOnly();
                    }
                });
            }

            // Track consent events (respects analytics consent)
            trackConsentEvent(action) {
                try {
                    if (typeof gtag === 'function') {
                        gtag('event', 'consent_banner_action', {
                            action: action,
                            timestamp: new Date().toISOString()
                        });
                    }
                } catch (error) {
                    console.warn('Consent tracking error:', error);
                }
            }

            // Get current consent status
            getConsentStatus() {
                return { ...this.consentData };
            }

            // Withdraw all consent
            withdrawConsent() {
                this.setConsent('analytics', false);
                this.setConsent('marketing', false);
                this.setConsent('functional', false);
                console.log('🚫 All consent withdrawn');
            }

            // Show consent settings modal
            showConsentSettings() {
                const existingModal = document.getElementById('consent-settings-modal');
                if (existingModal) {
                    existingModal.classList.add('show');
                    return;
                }

                this.createConsentSettingsModal();
            }

            // Hide consent settings modal
            hideConsentSettings() {
                const modal = document.getElementById('consent-settings-modal');
                if (modal) {
                    modal.classList.remove('show');
                }
            }

            // Create consent settings modal
            createConsentSettingsModal() {
                const modal = document.createElement('div');
                modal.id = 'consent-settings-modal';
                modal.className = 'consent-settings-modal';
                modal.setAttribute('role', 'dialog');
                modal.setAttribute('aria-labelledby', 'consent-settings-title');
                modal.setAttribute('aria-modal', 'true');

                const categories = this.cookieCategories;
                const currentConsent = this.consentData;

                modal.innerHTML = `
                    <div class="consent-settings-content">
                        <div class="consent-settings-header">
                            <h2 id="consent-settings-title" class="consent-settings-title">Préférences de cookies</h2>
                            <button class="consent-close-btn" aria-label="Fermer les préférences">×</button>
                        </div>

                        <div class="consent-categories">
                            ${Object.entries(categories).map(([key, category]) => `
                                <div class="consent-category">
                                    <div class="consent-category-header">
                                        <h3 class="consent-category-title">${category.name}</h3>
                                        <label class="consent-toggle">
                                            <input type="checkbox"
                                                   id="consent-${key}"
                                                   ${currentConsent[key] ? 'checked' : ''}
                                                   ${category.required ? 'disabled' : ''}
                                                   data-category="${key}">
                                            <span class="consent-toggle-slider"></span>
                                        </label>
                                    </div>
                                    <p class="consent-category-description">${category.description}</p>
                                    ${category.cookies.length > 0 ? `
                                        <div class="consent-cookie-list">
                                            <strong>Cookies:</strong> ${category.cookies.map(cookie => cookie.name).join(', ')}
                                        </div>
                                    ` : ''}
                                </div>
                            `).join('')}
                        </div>

                        <div class="consent-settings-actions">
                            <button class="consent-btn consent-btn-secondary" id="consent-save-settings">
                                Sauvegarder les préférences
                            </button>
                            <button class="consent-btn consent-btn-primary" id="consent-accept-all-modal">
                                Tout accepter
                            </button>
                        </div>
                    </div>
                `;

                // Add event listeners
                this.setupModalEventListeners(modal);

                // Add to DOM and show
                document.body.appendChild(modal);
                setTimeout(() => {
                    modal.classList.add('show');
                }, 50);

                // Focus management
                const firstToggle = modal.querySelector('input[type="checkbox"]:not([disabled])');
                if (firstToggle) {
                    firstToggle.focus();
                }
            }

            // Setup event listeners for settings modal
            setupModalEventListeners(modal) {
                const closeBtn = modal.querySelector('.consent-close-btn');
                const saveBtn = modal.querySelector('#consent-save-settings');
                const acceptAllBtn = modal.querySelector('#consent-accept-all-modal');
                const toggles = modal.querySelectorAll('input[type="checkbox"]:not([disabled])');

                closeBtn?.addEventListener('click', () => {
                    this.hideConsentSettings();
                });

                saveBtn?.addEventListener('click', () => {
                    this.saveCustomConsent(modal);
                });

                acceptAllBtn?.addEventListener('click', () => {
                    this.acceptAll();
                    this.hideConsentSettings();
                    this.hideConsentBanner();
                });

                // Close on backdrop click
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        this.hideConsentSettings();
                    }
                });

                // Keyboard navigation
                modal.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        this.hideConsentSettings();
                    }
                });
            }

            // Save custom consent preferences
            saveCustomConsent(modal) {
                const toggles = modal.querySelectorAll('input[type="checkbox"]:not([disabled])');

                toggles.forEach(toggle => {
                    const category = toggle.dataset.category;
                    const granted = toggle.checked;
                    this.setConsent(category, granted);
                });

                this.hideConsentSettings();
                this.hideConsentBanner();
                this.trackConsentEvent('custom_saved');

                console.log('✅ Custom consent preferences saved');
            }
        }

        // Initialize HAI Consent Manager
        window.haiConsent = new HAIConsentManager();

        // Cookie Management API - Public interface for other scripts
        window.HAICookies = {
            // Check if consent is given for a category
            hasConsent: (category) => window.haiConsent.hasConsent(category),

            // Get current consent status
            getConsentStatus: () => window.haiConsent.getConsentStatus(),

            // Set consent for a category (programmatic)
            setConsent: (category, granted) => window.haiConsent.setConsent(category, granted),

            // Accept all cookies
            acceptAll: () => window.haiConsent.acceptAll(),

            // Accept only essential cookies
            acceptEssentialOnly: () => window.haiConsent.acceptEssentialOnly(),

            // Withdraw all consent
            withdrawConsent: () => window.haiConsent.withdrawConsent(),

            // Show consent banner manually
            showBanner: () => window.haiConsent.showConsentBanner(),

            // Hide consent banner manually
            hideBanner: () => window.haiConsent.hideConsentBanner(),

            // Register callback for consent changes
            onConsentChange: (callback) => window.haiConsent.onConsentChange(callback),

            // Get cookie categories information
            getCookieCategories: () => window.haiConsent.cookieCategories,

            // Check if consent banner is needed
            needsConsent: () => window.haiConsent.needsConsent(),

            // Safe analytics tracking (respects consent)
            track: (eventName, params) => {
                if (window.haiConsent.hasConsent('analytics') && typeof gtag === 'function') {
                    gtag('event', eventName, params || {});
                }
            }
        };

        // Setup manage cookies link
        document.addEventListener('DOMContentLoaded', function() {
            const manageCookiesLink = document.getElementById('manage-cookies-link');
            if (manageCookiesLink) {
                manageCookiesLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.haiConsent.showConsentSettings();
                });
            }
        });

        // Make it available globally for debugging
        if (window.location.hostname === 'localhost' || window.location.hostname.includes('staging')) {
            console.log('🔧 HAI Consent Manager available as window.haiConsent for debugging');
            console.log('🔧 Cookie Management API available as window.HAICookies');
        }
    </script>

    <!-- Staging Environment Detection - Cache Bust v3.0 - 2025-09-15-16:30 -->
    <script>
      (function() {
        /**
         * Detects staging environment and configures SEO indexing
         * This replaces the React-based configureSEOIndexing function
         */
        function configureSEOIndexing() {
          // Detect staging environment by hostname
          const hostname = window.location.hostname;
          const isStaging = hostname.includes('staging') ||
                           hostname.includes('deploy-preview') ||
                           hostname.includes('netlify.app') ||
                           hostname.includes('localhost') ||
                           hostname.includes('127.0.0.1');

          if (isStaging) {
            // Add noindex, nofollow meta tag for staging
            const existingRobotsTag = document.querySelector('meta[name="robots"]');

            if (!existingRobotsTag) {
              const robotsTag = document.createElement('meta');
              robotsTag.name = 'robots';
              robotsTag.content = 'noindex, nofollow';
              document.head.appendChild(robotsTag);
            } else {
              // Update existing robots tag
              existingRobotsTag.setAttribute('content', 'noindex, nofollow');
            }

            console.log('🚫 Staging environment: Search engine indexing disabled');
            console.log('🔍 Environment details:', {
              hostname: hostname,
              isStaging: isStaging,
              robotsTag: document.querySelector('meta[name="robots"]')?.content
            });
          } else {
            // Production: Remove any noindex tags that might exist
            const robotsTag = document.querySelector('meta[name="robots"]');
            if (robotsTag && robotsTag.getAttribute('content')?.includes('noindex')) {
              robotsTag.remove();
            }

            console.log('✅ Production environment: Search engine indexing enabled');
          }
        }

        // Run immediately when script loads
        configureSEOIndexing();

        // Also run when DOM is ready (in case head modifications need DOM to be ready)
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', configureSEOIndexing);
        }
      })();
    </script>


    <script>
      /* ========================================
         A/B TESTING SYSTEM FOR HERO VARIANTS
         ========================================

         This system implements A/B testing for hero section content with:

         FEATURES:
         - Multiple hero variants (A, B, C, D) with different messaging
         - URL parameter control (?ab=random, ?ab=B, etc.)
         - localStorage persistence (respects functional cookie consent)
         - Analytics tracking for variant performance
         - Consent-aware implementation (GDPR compliant)

         VARIANTS:
         - Variant A: Default professional messaging
         - Variant B: More direct/aggressive approach
         - Variant C: Alternative positioning
         - Variant D: Additional test variant

         CONSENT INTEGRATION:
         - Only saves to localStorage if functional cookies consented
         - Only tracks analytics if analytics cookies consented
         - Graceful fallback to variant A if no consent

         USAGE:
         - ?ab=random - Random variant selection
         - ?ab=B - Force specific variant
         - Default behavior - Use saved variant or fallback to A
         ======================================== */

      const heroVariants = {
        A: {
          title: 'On repère. On automatise. <br><span class="neon-text">On fait gagner de l\'argent.</span>',
          subtitle: "Solutions (automatisation et IA) clés en main pour repreneurs et PME : diagnostic gratuit de 30 minutes et plan d'action concret."
        },
        B: {
          title: 'Rentabilisez votre reprise. <br><span class="neon-text">Sans perdre de temps.</span>',
          subtitle: 'Automatisation et IA appliquées à vos opérations : moins de coûts, plus de marge. Diagnostic offert, sans engagement.'
        },
        C: {
          title: 'Chaque minute compte. <br><span class="neon-text">Chaque euro aussi.</span>',
          subtitle: 'Nous analysons, optimisons et automatisons vos process. Résultat : efficacité immédiate et ROI tangible.'
        },
        D: {
          title: 'On ne vend pas des slides. <br><span class="neon-text">On livre des preuves.</span>',
          subtitle: 'Donnez-nous un vrai problème, on vous rend une vraie solution. Gratuit. Sans engagement.'
        }
      };

      function setHero(variant) {
        const titleContainer = document.getElementById('hero-title-container');
        const subtitleContainer = document.getElementById('hero-subtitle-container');
        if (!titleContainer || !subtitleContainer) return;
        titleContainer.innerHTML = `<h1 class="text-hero hero-title">${heroVariants[variant].title}</h1>`;
        subtitleContainer.innerHTML = `<p class="text-lg hero-subtitle">${heroVariants[variant].subtitle}</p>`;

        const heroContent = document.querySelector('.hero-content');
        if (heroContent) {
          // ensure repaint and then show
          requestAnimationFrame(() => heroContent.classList.add('js-visible'));
        }
      }

      document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const abParam = urlParams.get('ab'); // 'random' or 'B' or 'C'

        // Only use localStorage for hero_variant if functional cookies are consented
        let variant = null;
        if (window.haiConsent && window.haiConsent.hasConsent('functional')) {
          variant = localStorage.getItem('hero_variant');
        }

        if (!variant) {
          if (abParam === 'random') {
            const keys = Object.keys(heroVariants);
            variant = keys[Math.floor(Math.random() * keys.length)];
          } else if (abParam === 'B' || abParam === 'A' || abParam === 'D') {
            variant = abParam;
          } else {
            // default : C
            variant = 'C';
          }

          // Only save to localStorage if functional cookies are consented
          if (window.haiConsent && window.haiConsent.hasConsent('functional')) {
            localStorage.setItem('hero_variant', variant);
          }
        }

        try {
          // Only track if analytics consent is given
          if (window.haiConsent && window.haiConsent.hasConsent('analytics') && typeof gtag === 'function') {
            gtag('event', 'hero_variant_shown', { variant: variant });
          }
        } catch (error) {
          console.warn('Analytics tracking error:', error);
        }

        setHero(variant);

        const ctaButton = document.getElementById('hero-cta-button');
        if (ctaButton) {
          ctaButton.addEventListener('click', function() {
            try {
              // Only track if analytics consent is given
              if (window.haiConsent && window.haiConsent.hasConsent('analytics') && typeof gtag === 'function') {
                // Get variant respecting functional cookie consent
                const variant = (window.haiConsent.hasConsent('functional') ? localStorage.getItem('hero_variant') : null) || 'A';
                gtag('event', 'cta_clicked', { variant: variant });
              }
            } catch (error) {
              console.warn('Analytics tracking error:', error);
            }
          });
        }
      });
    </script>

<!-- Success Modal (hidden by default) -->
<div id="success-modal" class="success-modal">
  <div class="success-modal-content">
    <div class="success-icon">
      <i class="fas fa-check-circle"></i>
    </div>
    <h3 class="success-title">Demande reçue !</h3>
    <p class="success-message">Merci, nous revenons vers vous sous 24h pour planifier le diagnostic gratuit.</p>
    <div class="success-features">
      <div class="success-feature">
        <i class="fas fa-clock"></i>
        <span>Réponse sous 24h</span>
      </div>
      <div class="success-feature">
        <i class="fas fa-gift"></i>
        <span>100% gratuit</span>
      </div>
      <div class="success-feature">
        <i class="fas fa-handshake"></i>
        <span>Sans engagement</span>
      </div>
    </div>
    <button class="btn btn-primary btn-lg" onclick="closeSuccessModal()">
      <i class="fas fa-thumbs-up"></i>
      Parfait !
    </button>
  </div>
</div>

<script>
  function showSuccessModal(){
    const modal = document.getElementById('success-modal');
    if(modal) {
      modal.classList.add('show');
      document.body.style.overflow = 'hidden';
    }
  }

  function closeSuccessModal(){
    const modal = document.getElementById('success-modal');
    if(modal) {
      modal.classList.remove('show');
      document.body.style.overflow = '';
    }
  }

  // Fermer le modal en cliquant en dehors
  document.addEventListener('click', function(e) {
    const modal = document.getElementById('success-modal');
    if(e.target === modal) {
      closeSuccessModal();
    }
  });

  // Fermer le modal avec Escape
  document.addEventListener('keydown', function(e) {
    if(e.key === 'Escape') {
      closeSuccessModal();
    }
  });
</script>

<!-- Script pour le modal Exemples représentatifs -->
<script>
(function() {
  // Éléments du modal
  const section = document.getElementById('exemples');
  const modalOverlay = document.getElementById('exemples-modal');
  const closeBtn = document.getElementById('exemples-modal-close');
  const contactBtn = document.getElementById('exemples-modal-contact');

  if (!section || !modalOverlay || !closeBtn || !contactBtn) {
    console.warn('Exemples modal: Required elements not found');
    return;
  }

  // Using global safeGtag helper

  // Fonction d'initialisation du modal (déjà visible par défaut)
  function initExemplesModal() {
    // Le modal est déjà actif par défaut, on s'assure juste qu'il est bien configuré
    modalOverlay.setAttribute('aria-hidden', 'false');

    // Analytics pour le chargement initial
    safeGtag('exemples_modal_loaded', {
      section: 'exemples-representatifs',
      trigger: 'page_load'
    });

    console.log('Exemples modal: Modal initialized and visible by default');
  }

  // Fonction pour gérer le focus quand l'utilisateur navigue vers la section
  function handleSectionFocus() {
    // Si l'utilisateur navigue vers la section via un lien, donner le focus au modal
    if (window.location.hash === '#exemples' && modalOverlay.classList.contains('active')) {
      setTimeout(() => {
        contactBtn.focus();
      }, 100);
    }
  }

  // Fonction pour fermer le modal
  function hideExemplesModal() {
    modalOverlay.classList.remove('active');
    modalOverlay.setAttribute('aria-hidden', 'true');

    // Analytics
    safeGtag('exemples_modal_closed', {
      action: 'view_examples'
    });

    console.log('Exemples modal: Modal closed - showing examples');
  }

  // Fonction pour rediriger vers le contact
  function redirectToContact() {
    // Analytics
    safeGtag('exemples_modal_contact', {
      action: 'redirect_to_contact'
    });

    // Fermer le modal d'abord
    modalOverlay.classList.remove('active');
    modalOverlay.setAttribute('aria-hidden', 'true');

    // Rediriger vers le formulaire de contact
    setTimeout(() => {
      window.location.href = '#contact';
    }, 200);

    console.log('Exemples modal: Redirecting to contact form');
  }

  // Event listeners pour les boutons
  closeBtn.addEventListener('click', hideExemplesModal);
  contactBtn.addEventListener('click', redirectToContact);

  // Gestion du clavier pour l'accessibilité
  modalOverlay.addEventListener('keydown', function(e) {
    if (e.key === 'Tab') {
      // Garder le focus dans le modal
      const focusableElements = modalOverlay.querySelectorAll('button');
      const firstElement = focusableElements[0];
      const lastElement = focusableElements[focusableElements.length - 1];

      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      } else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  });

  // Initialiser le modal au chargement de la page
  document.addEventListener('DOMContentLoaded', function() {
    initExemplesModal();
    handleSectionFocus();
  });

  // Gérer la navigation vers la section
  window.addEventListener('hashchange', handleSectionFocus);

  // Si le DOM est déjà chargé, initialiser immédiatement
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initExemplesModal);
  } else {
    initExemplesModal();
    handleSectionFocus();
  }

  console.log('Exemples modal: Script initialized - modal visible by default');
})();
</script>

<script>
(function(){
  // Using global safeGtag helper

  // Elements
  const cards = document.querySelectorAll('.case-study-card');
  const modal = document.getElementById('case-modal');
  const modalTitle = document.getElementById('case-modal-title');
  const modalBody = document.getElementById('case-modal-body');
  const modalClose = document.getElementById('case-modal-close');
  const modalPdf = document.getElementById('case-modal-pdf');
  const modalCta = document.getElementById('case-modal-cta');

  if (!cards.length || !modal) return;

  // Open modal with content from .case-data
  function openCaseModal(card){
    const id = card.getAttribute('data-case-id') || 'unknown';
    const titleEl = card.querySelector('h3');
    const dataEl = card.querySelector('.case-data');
    modalTitle.innerHTML = titleEl ? titleEl.innerHTML : 'Étude de cas';
    modalBody.innerHTML = dataEl ? dataEl.innerHTML : '<p>Contenu indisponible.</p>';
    modal.style.display = 'flex';
    modal.setAttribute('aria-hidden','false');
    modalCta.focus();
    safeGtag('case_modal_open', { case_id: id });
    // set PDF href placeholder (to be replaced server-side)
    modalPdf.setAttribute('href', `/pdfs/${id}.pdf`);
    modalPdf.setAttribute('data-case-id', id);
    modalCta.setAttribute('data-case-id', id);
  }

  function closeCaseModal(){
    modal.style.display = 'none';
    modal.setAttribute('aria-hidden','true');
    safeGtag('case_modal_close', {});
  }

  // Click listeners on cards
  cards.forEach(card => {
    card.addEventListener('click', function(e){
      // Avoid clicks on links inside card
      if (e.target.closest('a')) return;
      openCaseModal(card);
      safeGtag('case_card_clicked', { case_id: card.getAttribute('data-case-id') });
    });
    // keyboard accessible (Enter)
    card.addEventListener('keydown', function(e){
      if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); openCaseModal(card); }
    });
  });

  // CTA tracking in modal
  modalCta && modalCta.addEventListener('click', function(){
    const id = this.getAttribute('data-case-id') || 'unknown';
    safeGtag('case_modal_cta', { case_id: id });
    // scroll to contact section after a little delay for tracking
    setTimeout(()=> { window.location.href = '#contact'; }, 150);
  });

  // PDF click tracking
  modalPdf && modalPdf.addEventListener('click', function(){
    const id = this.getAttribute('data-case-id') || 'unknown';
    safeGtag('case_modal_pdf', { case_id: id });
  });

  // Close handlers
  modalClose && modalClose.addEventListener('click', closeCaseModal);
  modal.addEventListener('click', function(e){ if (e.target === modal) closeCaseModal(); });
  document.addEventListener('keydown', function(e){ if (e.key === 'Escape' && modal.style.display === 'flex') closeCaseModal(); });

})();
</script>

<!-- Modal - Case Study -->
<div id="case-modal" aria-hidden="true" role="dialog" aria-modal="true" style="display:none; position:fixed; inset:0; z-index:10050; align-items:center; justify-content:center; background:rgba(0,0,0,0.65);">
  <div role="document" style="width:95%; max-width:760px; background:var(--glass-bg); border:1px solid var(--glass-border); padding:1.5rem; border-radius:12px; position:relative; box-shadow:var(--shadow-hard);">
    <button id="case-modal-close" aria-label="Fermer" style="position:absolute; right:12px; top:12px; background:none; border:none; color:var(--text-muted); font-size:1.4rem; cursor:pointer;">&times;</button>
    <h3 id="case-modal-title" class="text-xl" style="margin-bottom:0.5rem; color:var(--text-primary)"></h3>
    <div id="case-modal-body" style="color:var(--text-muted); margin-bottom:1rem;"></div>
    <div style="display:flex; gap:0.75rem; justify-content:flex-end; align-items:center; margin-top:1rem;">
      <a id="case-modal-cta" class="btn btn-primary" href="#contact" style="text-decoration:none;">Réserver un diagnostic — 30 min</a>
      <a id="case-modal-pdf" class="btn btn-secondary" href="#" target="_blank" style="text-decoration:none;">Télécharger le résumé (PDF)</a>
    </div>
  </div>
</div>

<!-- Global Error Handling -->
<script>
(function() {
    // Suppress service worker connection errors (often from browser extensions)
    const originalConsoleError = console.error;
    console.error = function(...args) {
        const message = args.join(' ');

        // Filter out known non-critical errors
        if (message.includes('Failed to connect with service worker') ||
            message.includes('getRuleList') ||
            message.includes('content.js') ||
            message.includes('extension')) {
            // Silently ignore these errors as they're typically from browser extensions
            return;
        }

        // Log all other errors normally
        originalConsoleError.apply(console, args);
    };

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
        const message = event.reason?.message || event.reason || 'Unknown error';

        // Filter out extension-related errors
        if (message.includes('extension') ||
            message.includes('content script') ||
            message.includes('service worker')) {
            event.preventDefault(); // Prevent the error from being logged
            return;
        }

        console.error('Unhandled promise rejection:', event.reason);
    });

    // Handle global errors
    window.addEventListener('error', function(event) {
        const message = event.message || '';

        // Filter out extension-related errors
        if (message.includes('extension') ||
            message.includes('content script') ||
            event.filename?.includes('extension')) {
            return;
        }

        console.error('Global error:', event.error || event.message);
    });
})();
</script>

<noscript>
    <div style="background: var(--bg-secondary); color: var(--text-primary); text-align: center; padding: 1rem;">
        <p>JavaScript est désactivé dans votre navigateur. Certaines fonctionnalités de ce site peuvent ne pas fonctionner correctement.</p>
    </div>
</noscript>
</body>
</html>
